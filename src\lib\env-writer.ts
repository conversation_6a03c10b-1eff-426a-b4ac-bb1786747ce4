// Environment Variables Writer Service
// This service helps generate .env file content for configuration

import { MySQLConfig } from './env-config';

// Generate .env file content for MySQL configuration
export const generateEnvContent = (mysqlConfig: MySQLConfig): string => {
  const envContent = `# Supabase Configuration
VITE_SUPABASE_URL=${import.meta.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co'}
VITE_SUPABASE_ANON_KEY=${import.meta.env.VITE_SUPABASE_ANON_KEY || 'your_supabase_anon_key'}

# MySQL Database Configuration
VITE_MYSQL_HOST=${mysqlConfig.host}
VITE_MYSQL_PORT=${mysqlConfig.port}
VITE_MYSQL_DATABASE=${mysqlConfig.database}
VITE_MYSQL_USERNAME=${mysqlConfig.username}
VITE_MYSQL_PASSWORD=${mysqlConfig.password}

# MySQL Advanced Configuration
VITE_MYSQL_POOL_SIZE=${mysqlConfig.poolSize}
VITE_MYSQL_TIMEOUT=${mysqlConfig.timeout}
VITE_MYSQL_CHARSET=${mysqlConfig.charset}
VITE_MYSQL_SSL_MODE=${mysqlConfig.sslMode}

# Application Configuration
VITE_APP_NAME=${import.meta.env.VITE_APP_NAME || 'SIMASET UWG'}
VITE_APP_VERSION=${import.meta.env.VITE_APP_VERSION || '1.0.0'}
VITE_APP_ENVIRONMENT=${import.meta.env.VITE_APP_ENVIRONMENT || 'development'}

# Security Configuration
VITE_ENCRYPTION_KEY=${import.meta.env.VITE_ENCRYPTION_KEY || ''}
VITE_JWT_SECRET=${import.meta.env.VITE_JWT_SECRET || ''}

# Backup Configuration
VITE_BACKUP_RETENTION_DAYS=${import.meta.env.VITE_BACKUP_RETENTION_DAYS || '30'}
VITE_AUTO_BACKUP_ENABLED=${import.meta.env.VITE_AUTO_BACKUP_ENABLED || 'true'}
VITE_BACKUP_SCHEDULE=${import.meta.env.VITE_BACKUP_SCHEDULE || 'daily'}

# Other Database Providers (Optional)
VITE_AIVEN_CONNECTION_STRING=${import.meta.env.VITE_AIVEN_CONNECTION_STRING || ''}
VITE_NEONDB_CONNECTION_STRING=${import.meta.env.VITE_NEONDB_CONNECTION_STRING || ''}
VITE_POSTGRESQL_CONNECTION_STRING=${import.meta.env.VITE_POSTGRESQL_CONNECTION_STRING || ''}`;

  return envContent;
};

// Generate only MySQL configuration part
export const generateMySQLEnvContent = (mysqlConfig: MySQLConfig): string => {
  return `# MySQL Database Configuration
VITE_MYSQL_HOST=${mysqlConfig.host}
VITE_MYSQL_PORT=${mysqlConfig.port}
VITE_MYSQL_DATABASE=${mysqlConfig.database}
VITE_MYSQL_USERNAME=${mysqlConfig.username}
VITE_MYSQL_PASSWORD=${mysqlConfig.password}

# MySQL Advanced Configuration
VITE_MYSQL_POOL_SIZE=${mysqlConfig.poolSize}
VITE_MYSQL_TIMEOUT=${mysqlConfig.timeout}
VITE_MYSQL_CHARSET=${mysqlConfig.charset}
VITE_MYSQL_SSL_MODE=${mysqlConfig.sslMode}`;
};

// Download .env file with current configuration
export const downloadEnvFile = (mysqlConfig: MySQLConfig, filename: string = '.env'): void => {
  const envContent = generateEnvContent(mysqlConfig);
  const blob = new Blob([envContent], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// Copy .env content to clipboard
export const copyEnvToClipboard = async (mysqlConfig: MySQLConfig): Promise<boolean> => {
  try {
    const envContent = generateEnvContent(mysqlConfig);
    await navigator.clipboard.writeText(envContent);
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
};

// Get current environment variables as object
export const getCurrentEnvVars = (): Record<string, string> => {
  const envVars: Record<string, string> = {};
  
  // Get all VITE_ prefixed environment variables
  Object.keys(import.meta.env).forEach(key => {
    if (key.startsWith('VITE_')) {
      envVars[key] = import.meta.env[key] || '';
    }
  });
  
  return envVars;
};

// Check if configuration has changed from environment
export const hasConfigChanged = (mysqlConfig: MySQLConfig): boolean => {
  const currentConfig = {
    host: import.meta.env.VITE_MYSQL_HOST || 'localhost',
    port: import.meta.env.VITE_MYSQL_PORT || '3306',
    database: import.meta.env.VITE_MYSQL_DATABASE || '',
    username: import.meta.env.VITE_MYSQL_USERNAME || 'root',
    password: import.meta.env.VITE_MYSQL_PASSWORD || '',
    poolSize: import.meta.env.VITE_MYSQL_POOL_SIZE || '10',
    timeout: import.meta.env.VITE_MYSQL_TIMEOUT || '30',
    charset: import.meta.env.VITE_MYSQL_CHARSET || 'utf8mb4',
    sslMode: import.meta.env.VITE_MYSQL_SSL_MODE || 'disabled'
  };
  
  return JSON.stringify(currentConfig) !== JSON.stringify(mysqlConfig);
};
