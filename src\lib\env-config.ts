// Environment Configuration Management
// This file handles reading and writing environment variables for the application

export interface MySQLConfig {
  host: string;
  port: string;
  database: string;
  username: string;
  password: string;
  poolSize: string;
  timeout: string;
  charset: string;
  sslMode: string;
}

export interface AppConfig {
  name: string;
  version: string;
  environment: string;
}

export interface BackupConfig {
  retentionDays: string;
  autoBackupEnabled: boolean;
  schedule: string;
}

// Get MySQL configuration from environment variables
export const getMySQLConfig = (): MySQLConfig => {
  return {
    host: import.meta.env.VITE_MYSQL_HOST || 'localhost',
    port: import.meta.env.VITE_MYSQL_PORT || '3306',
    database: import.meta.env.VITE_MYSQL_DATABASE || '',
    username: import.meta.env.VITE_MYSQL_USERNAME || 'root',
    password: import.meta.env.VITE_MYSQL_PASSWORD || '',
    poolSize: import.meta.env.VITE_MYSQL_POOL_SIZE || '10',
    timeout: import.meta.env.VITE_MYSQL_TIMEOUT || '30',
    charset: import.meta.env.VITE_MYSQL_CHARSET || 'utf8mb4',
    sslMode: import.meta.env.VITE_MYSQL_SSL_MODE || 'disabled'
  };
};

// Get application configuration
export const getAppConfig = (): AppConfig => {
  return {
    name: import.meta.env.VITE_APP_NAME || 'SIMASET UWG',
    version: import.meta.env.VITE_APP_VERSION || '1.0.0',
    environment: import.meta.env.VITE_APP_ENVIRONMENT || 'development'
  };
};

// Get backup configuration
export const getBackupConfig = (): BackupConfig => {
  return {
    retentionDays: import.meta.env.VITE_BACKUP_RETENTION_DAYS || '30',
    autoBackupEnabled: import.meta.env.VITE_AUTO_BACKUP_ENABLED === 'true',
    schedule: import.meta.env.VITE_BACKUP_SCHEDULE || 'daily'
  };
};

// Generate MySQL connection string
export const generateMySQLConnectionString = (config: MySQLConfig): string => {
  const { host, port, database, username, password, charset, sslMode } = config;
  
  if (!database || !username) {
    return 'mysql://username:password@host:port/database?charset=utf8mb4&ssl=disabled';
  }
  
  const passwordPart = password ? `:${password}` : '';
  return `mysql://${username}${passwordPart}@${host}:${port}/${database}?charset=${charset}&ssl=${sslMode}`;
};

// Validate MySQL configuration
export const validateMySQLConfig = (config: MySQLConfig): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!config.host.trim()) {
    errors.push('Host tidak boleh kosong');
  }
  
  if (!config.port.trim() || isNaN(Number(config.port))) {
    errors.push('Port harus berupa angka yang valid');
  }
  
  if (!config.database.trim()) {
    errors.push('Nama database tidak boleh kosong');
  }
  
  if (!config.username.trim()) {
    errors.push('Username tidak boleh kosong');
  }
  
  if (!config.poolSize.trim() || isNaN(Number(config.poolSize))) {
    errors.push('Pool size harus berupa angka yang valid');
  }
  
  if (!config.timeout.trim() || isNaN(Number(config.timeout))) {
    errors.push('Timeout harus berupa angka yang valid');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Get Supabase configuration
export const getSupabaseConfig = () => {
  return {
    url: import.meta.env.VITE_SUPABASE_URL || '',
    anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY || ''
  };
};

// Get other database connection strings
export const getOtherDatabaseConfigs = () => {
  return {
    aiven: import.meta.env.VITE_AIVEN_CONNECTION_STRING || '',
    neondb: import.meta.env.VITE_NEONDB_CONNECTION_STRING || '',
    postgresql: import.meta.env.VITE_POSTGRESQL_CONNECTION_STRING || ''
  };
};

// Check if running in development mode
export const isDevelopment = (): boolean => {
  return import.meta.env.DEV || import.meta.env.VITE_APP_ENVIRONMENT === 'development';
};

// Check if running in production mode
export const isProduction = (): boolean => {
  return import.meta.env.PROD || import.meta.env.VITE_APP_ENVIRONMENT === 'production';
};
