// MySQL Client for SIMASET UWG
// This file provides MySQL database operations to replace Supabase

import { getMySQLConfig } from './env-config';

// Database configuration
const getConfig = () => {
  const config = getMySQLConfig();
  return {
    host: config.host,
    port: parseInt(config.port),
    database: config.database,
    username: config.username,
    password: config.password,
    charset: config.charset,
    ssl: config.sslMode !== 'disabled'
  };
};

// Database connection string
export const getConnectionString = () => {
  const config = getConfig();
  const passwordPart = config.password ? `:${config.password}` : '';
  return `mysql://${config.username}${passwordPart}@${config.host}:${config.port}/${config.database}?charset=${config.charset}`;
};

// Base API URL for MySQL operations
const API_BASE_URL = '/api/mysql';

// Generic API request function
const apiRequest = async (endpoint: string, options: RequestInit = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  const config = getConfig();
  
  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'X-DB-Host': config.host,
      'X-DB-Port': config.port.toString(),
      'X-DB-Name': config.database,
      'X-DB-User': config.username,
      'X-DB-Pass': config.password,
      ...options.headers,
    },
  });

  if (!response.ok) {
    throw new Error(`API request failed: ${response.statusText}`);
  }

  return response.json();
};

// Assets operations
export const assetsAPI = {
  // Get all assets
  async getAll() {
    return apiRequest('/assets');
  },

  // Get asset by ID
  async getById(id: string) {
    return apiRequest(`/assets/${id}`);
  },

  // Create new asset
  async create(asset: any) {
    return apiRequest('/assets', {
      method: 'POST',
      body: JSON.stringify(asset),
    });
  },

  // Update asset
  async update(id: string, asset: any) {
    return apiRequest(`/assets/${id}`, {
      method: 'PUT',
      body: JSON.stringify(asset),
    });
  },

  // Delete asset
  async delete(id: string) {
    return apiRequest(`/assets/${id}`, {
      method: 'DELETE',
    });
  },

  // Search assets
  async search(query: string) {
    return apiRequest(`/assets/search?q=${encodeURIComponent(query)}`);
  },

  // Get assets by category
  async getByCategory(category: string) {
    return apiRequest(`/assets/category/${encodeURIComponent(category)}`);
  },

  // Get assets by location
  async getByLocation(location: string) {
    return apiRequest(`/assets/location/${encodeURIComponent(location)}`);
  },
};

// Categories operations
export const categoriesAPI = {
  // Get all categories
  async getAll() {
    return apiRequest('/categories');
  },

  // Create new category
  async create(category: any) {
    return apiRequest('/categories', {
      method: 'POST',
      body: JSON.stringify(category),
    });
  },

  // Update category
  async update(id: string, category: any) {
    return apiRequest(`/categories/${id}`, {
      method: 'PUT',
      body: JSON.stringify(category),
    });
  },

  // Delete category
  async delete(id: string) {
    return apiRequest(`/categories/${id}`, {
      method: 'DELETE',
    });
  },
};

// Locations operations
export const locationsAPI = {
  // Get all locations
  async getAll() {
    return apiRequest('/locations');
  },

  // Create new location
  async create(location: any) {
    return apiRequest('/locations', {
      method: 'POST',
      body: JSON.stringify(location),
    });
  },

  // Update location
  async update(id: string, location: any) {
    return apiRequest(`/locations/${id}`, {
      method: 'PUT',
      body: JSON.stringify(location),
    });
  },

  // Delete location
  async delete(id: string) {
    return apiRequest(`/locations/${id}`, {
      method: 'DELETE',
    });
  },
};

// Users operations
export const usersAPI = {
  // Get all users
  async getAll() {
    return apiRequest('/users');
  },

  // Get user by ID
  async getById(id: string) {
    return apiRequest(`/users/${id}`);
  },

  // Create new user
  async create(user: any) {
    return apiRequest('/users', {
      method: 'POST',
      body: JSON.stringify(user),
    });
  },

  // Update user
  async update(id: string, user: any) {
    return apiRequest(`/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(user),
    });
  },

  // Delete user
  async delete(id: string) {
    return apiRequest(`/users/${id}`, {
      method: 'DELETE',
    });
  },
};

// Documents operations
export const documentsAPI = {
  // Get all documents
  async getAll() {
    return apiRequest('/documents');
  },

  // Get document by ID
  async getById(id: string) {
    return apiRequest(`/documents/${id}`);
  },

  // Upload document
  async upload(formData: FormData) {
    const config = getConfig();
    return fetch(`${API_BASE_URL}/documents/upload`, {
      method: 'POST',
      headers: {
        'X-DB-Host': config.host,
        'X-DB-Port': config.port.toString(),
        'X-DB-Name': config.database,
        'X-DB-User': config.username,
        'X-DB-Pass': config.password,
      },
      body: formData,
    }).then(res => res.json());
  },

  // Update document
  async update(id: string, document: any) {
    return apiRequest(`/documents/${id}`, {
      method: 'PUT',
      body: JSON.stringify(document),
    });
  },

  // Delete document
  async delete(id: string) {
    return apiRequest(`/documents/${id}`, {
      method: 'DELETE',
    });
  },

  // Get documents by asset
  async getByAsset(assetId: string) {
    return apiRequest(`/documents/asset/${assetId}`);
  },
};

// Settings operations
export const settingsAPI = {
  // Get all settings
  async getAll() {
    return apiRequest('/settings');
  },

  // Get setting by key
  async getByKey(key: string) {
    return apiRequest(`/settings/${key}`);
  },

  // Update setting
  async update(key: string, value: any) {
    return apiRequest(`/settings/${key}`, {
      method: 'PUT',
      body: JSON.stringify({ value }),
    });
  },

  // Get public settings only
  async getPublic() {
    return apiRequest('/settings/public');
  },
};

// Database operations
export const databaseAPI = {
  // Test connection
  async testConnection() {
    return apiRequest('/test-connection');
  },

  // Get database info
  async getInfo() {
    return apiRequest('/info');
  },

  // Backup database
  async backup() {
    return apiRequest('/backup', {
      method: 'POST',
    });
  },

  // Restore database
  async restore(backupData: any) {
    return apiRequest('/restore', {
      method: 'POST',
      body: JSON.stringify(backupData),
    });
  },

  // Get statistics
  async getStats() {
    return apiRequest('/stats');
  },
};

// Migration operations
export const migrationAPI = {
  // Migrate data from Supabase to MySQL
  async migrateFromSupabase(data: any) {
    return apiRequest('/migration/from-supabase', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Get migration status
  async getStatus() {
    return apiRequest('/migration/status');
  },

  // Validate migration
  async validate() {
    return apiRequest('/migration/validate');
  },

  // Rollback migration
  async rollback() {
    return apiRequest('/migration/rollback', {
      method: 'POST',
    });
  },
};

// Export default MySQL client object
export const mysqlClient = {
  assets: assetsAPI,
  categories: categoriesAPI,
  locations: locationsAPI,
  users: usersAPI,
  documents: documentsAPI,
  settings: settingsAPI,
  database: databaseAPI,
  migration: migrationAPI,
};
