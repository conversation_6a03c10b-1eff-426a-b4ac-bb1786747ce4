import { FileText, File, Image, Archive } from 'lucide-react';

export const documentsData = [
  {
    id: 1,
    name: 'Sertifikat Tanah Kampus Utama.pdf',
    type: 'PDF',
    size: '2.4 MB',
    category: 'Sertifikat',
    uploadDate: '2024-01-15',
    uploadedBy: '<PERSON>. <PERSON>',
    status: 'Approved'
  },
  {
    id: 2,
    name: 'Dokumen Pembelian Lab Komputer.docx',
    type: 'Word',
    size: '1.2 MB',
    category: 'Pembelian',
    uploadDate: '2024-01-10',
    uploadedBy: 'Siti <PERSON>hay<PERSON>',
    status: 'Review'
  },
  {
    id: 3,
    name: 'Foto Gedung Rektorat.jpg',
    type: 'Image',
    size: '3.8 MB',
    category: 'Foto Aset',
    uploadDate: '2024-01-08',
    uploadedBy: '<PERSON><PERSON>',
    status: 'Approved'
  }
];

export const categoriesData = [
  { name: 'Serti<PERSON>kat', count: 25, icon: FileText, color: 'text-blue-600' },
  { name: '<PERSON>embelian', count: 18, icon: File, color: 'text-green-600' },
  { name: 'Foto Aset', count: 45, icon: Image, color: 'text-purple-600' },
  { name: '<PERSON><PERSON><PERSON>', count: 12, icon: Archive, color: 'text-orange-600' }
];