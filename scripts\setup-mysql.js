#!/usr/bin/env node

/**
 * MySQL Setup Script for SIMASET UWG
 * This script helps setup MySQL database and run migrations
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logStep(step, message) {
  log(`\n[${step}] ${message}`, 'blue');
}

function logSuccess(message) {
  log(`✓ ${message}`, 'green');
}

function logError(message) {
  log(`✗ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠ ${message}`, 'yellow');
}

// Check if MySQL is installed
function checkMySQLInstallation() {
  logStep('1', 'Checking MySQL installation...');
  
  try {
    execSync('mysql --version', { stdio: 'pipe' });
    logSuccess('MySQL is installed');
    return true;
  } catch (error) {
    logError('MySQL is not installed or not in PATH');
    log('\nPlease install MySQL first:', 'yellow');
    log('- Windows: Download from https://dev.mysql.com/downloads/mysql/', 'yellow');
    log('- macOS: brew install mysql', 'yellow');
    log('- Ubuntu: sudo apt install mysql-server', 'yellow');
    return false;
  }
}

// Read environment configuration
function readEnvConfig() {
  logStep('2', 'Reading environment configuration...');
  
  const envPath = path.join(process.cwd(), '.env');
  
  if (!fs.existsSync(envPath)) {
    logError('.env file not found');
    log('Please create .env file from .env.example', 'yellow');
    return null;
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const config = {};
  
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      config[key.trim()] = value.trim();
    }
  });

  const mysqlConfig = {
    host: config.VITE_MYSQL_HOST || 'localhost',
    port: config.VITE_MYSQL_PORT || '3306',
    database: config.VITE_MYSQL_DATABASE || 'simaset_db',
    username: config.VITE_MYSQL_USERNAME || 'root',
    password: config.VITE_MYSQL_PASSWORD || ''
  };

  logSuccess('Environment configuration loaded');
  log(`  Host: ${mysqlConfig.host}:${mysqlConfig.port}`, 'cyan');
  log(`  Database: ${mysqlConfig.database}`, 'cyan');
  log(`  Username: ${mysqlConfig.username}`, 'cyan');
  
  return mysqlConfig;
}

// Test MySQL connection
function testConnection(config) {
  logStep('3', 'Testing MySQL connection...');
  
  try {
    const connectionString = `mysql -h${config.host} -P${config.port} -u${config.username}`;
    const passwordFlag = config.password ? `-p${config.password}` : '';
    
    execSync(`${connectionString} ${passwordFlag} -e "SELECT 1"`, { stdio: 'pipe' });
    logSuccess('MySQL connection successful');
    return true;
  } catch (error) {
    logError('Failed to connect to MySQL');
    log('Please check your MySQL configuration in .env file', 'yellow');
    return false;
  }
}

// Create database if not exists
function createDatabase(config) {
  logStep('4', 'Creating database...');
  
  try {
    const connectionString = `mysql -h${config.host} -P${config.port} -u${config.username}`;
    const passwordFlag = config.password ? `-p${config.password}` : '';
    
    execSync(`${connectionString} ${passwordFlag} -e "CREATE DATABASE IF NOT EXISTS ${config.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"`, { stdio: 'pipe' });
    logSuccess(`Database '${config.database}' created/verified`);
    return true;
  } catch (error) {
    logError(`Failed to create database: ${error.message}`);
    return false;
  }
}

// Run migrations
function runMigrations(config) {
  logStep('5', 'Running database migrations...');
  
  const migrationsDir = path.join(process.cwd(), 'mysql-migrations');
  
  if (!fs.existsSync(migrationsDir)) {
    logError('Migrations directory not found');
    return false;
  }

  const migrationFiles = fs.readdirSync(migrationsDir)
    .filter(file => file.endsWith('.sql'))
    .sort();

  if (migrationFiles.length === 0) {
    logWarning('No migration files found');
    return true;
  }

  const connectionString = `mysql -h${config.host} -P${config.port} -u${config.username}`;
  const passwordFlag = config.password ? `-p${config.password}` : '';

  for (const file of migrationFiles) {
    try {
      const filePath = path.join(migrationsDir, file);
      log(`  Running: ${file}`, 'cyan');
      
      execSync(`${connectionString} ${passwordFlag} ${config.database} < "${filePath}"`, { stdio: 'pipe' });
      logSuccess(`  ✓ ${file} completed`);
    } catch (error) {
      logError(`  ✗ ${file} failed: ${error.message}`);
      return false;
    }
  }

  logSuccess('All migrations completed successfully');
  return true;
}

// Verify setup
function verifySetup(config) {
  logStep('6', 'Verifying setup...');
  
  try {
    const connectionString = `mysql -h${config.host} -P${config.port} -u${config.username}`;
    const passwordFlag = config.password ? `-p${config.password}` : '';
    
    // Check if tables exist
    const result = execSync(`${connectionString} ${passwordFlag} ${config.database} -e "SHOW TABLES"`, { encoding: 'utf8' });
    const tables = result.split('\n').filter(line => line.trim() && !line.includes('Tables_in_'));
    
    logSuccess(`Found ${tables.length} tables:`);
    tables.forEach(table => {
      log(`  - ${table}`, 'cyan');
    });
    
    // Get row counts
    for (const table of ['assets', 'categories', 'locations']) {
      if (tables.includes(table)) {
        try {
          const countResult = execSync(`${connectionString} ${passwordFlag} ${config.database} -e "SELECT COUNT(*) as count FROM ${table}"`, { encoding: 'utf8' });
          const count = countResult.split('\n')[1];
          log(`  ${table}: ${count} rows`, 'green');
        } catch (error) {
          log(`  ${table}: Error getting count`, 'yellow');
        }
      }
    }
    
    return true;
  } catch (error) {
    logError(`Verification failed: ${error.message}`);
    return false;
  }
}

// Main setup function
function main() {
  logHeader('SIMASET UWG - MySQL Database Setup');
  
  // Step 1: Check MySQL installation
  if (!checkMySQLInstallation()) {
    process.exit(1);
  }
  
  // Step 2: Read configuration
  const config = readEnvConfig();
  if (!config) {
    process.exit(1);
  }
  
  // Step 3: Test connection
  if (!testConnection(config)) {
    process.exit(1);
  }
  
  // Step 4: Create database
  if (!createDatabase(config)) {
    process.exit(1);
  }
  
  // Step 5: Run migrations
  if (!runMigrations(config)) {
    process.exit(1);
  }
  
  // Step 6: Verify setup
  if (!verifySetup(config)) {
    process.exit(1);
  }
  
  logHeader('Setup Completed Successfully!');
  log('\nNext steps:', 'green');
  log('1. Start your application: npm run dev', 'cyan');
  log('2. Go to Settings > Migration tab', 'cyan');
  log('3. Run migration from Supabase to MySQL', 'cyan');
  log('\nYour MySQL database is ready to use!', 'bright');
}

// Run the setup
if (require.main === module) {
  main();
}

module.exports = {
  checkMySQLInstallation,
  readEnvConfig,
  testConnection,
  createDatabase,
  runMigrations,
  verifySetup
};
