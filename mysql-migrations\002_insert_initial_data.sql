-- MySQL Initial Data Script for SIMASET UWG
-- Created: 2025-01-07
-- Description: Insert initial data for categories, locations, users, and sample assets

USE simaset_db;

-- Insert default categories
INSERT INTO categories (id, name, description, icon, color) VALUES
(UUID(), 'Komputer', 'Peralatan komputer dan laptop', 'Monitor', 'text-blue-600'),
(UUID(), 'Furniture', 'Meja, kursi, dan perabotan kantor', 'Armchair', 'text-green-600'),
(UUID(), 'Elektronik', 'Peralatan elektronik kantor', 'Zap', 'text-yellow-600'),
(UUID(), 'Kendaraan', 'Kendaraan operasional', 'Car', 'text-red-600'),
(UUID(), 'Peralatan', 'Peralatan dan tools kerja', 'Wrench', 'text-purple-600'),
(UUID(), 'Bangunan', 'Gedung dan infrastruktur', 'Building', 'text-gray-600'),
(UUID(), 'Teknologi', 'Perangkat teknologi dan komunikasi', 'Smartphone', 'text-indigo-600');

-- Insert default locations
INSERT INTO locations (id, name, description, address, building, floor, room) VALUES
(UUID(), 'Gedung A - Lt 1', 'Lantai 1 Gedung A', 'Jl. Raya Widyagama No. 1', 'Gedung A', 'Lantai 1', NULL),
(UUID(), 'Gedung A - Lt 2', 'Lantai 2 Gedung A', 'Jl. Raya Widyagama No. 1', 'Gedung A', 'Lantai 2', NULL),
(UUID(), 'Gedung A - Lt 3', 'Lantai 3 Gedung A', 'Jl. Raya Widyagama No. 1', 'Gedung A', 'Lantai 3', NULL),
(UUID(), 'Gedung B - Lt 1', 'Lantai 1 Gedung B', 'Jl. Raya Widyagama No. 2', 'Gedung B', 'Lantai 1', NULL),
(UUID(), 'Gedung B - Lt 2', 'Lantai 2 Gedung B', 'Jl. Raya Widyagama No. 2', 'Gedung B', 'Lantai 2', NULL),
(UUID(), 'Gedung C - Lt 1', 'Lantai 1 Gedung C', 'Jl. Raya Widyagama No. 3', 'Gedung C', 'Lantai 1', NULL),
(UUID(), 'Gedung C - Lt 2', 'Lantai 2 Gedung C', 'Jl. Raya Widyagama No. 3', 'Gedung C', 'Lantai 2', NULL),
(UUID(), 'Lab Komputer', 'Laboratorium Komputer', 'Gedung C Lt. 2', 'Gedung C', 'Lantai 2', 'Lab 201'),
(UUID(), 'Lab Bahasa', 'Laboratorium Bahasa', 'Gedung C Lt. 1', 'Gedung C', 'Lantai 1', 'Lab 101'),
(UUID(), 'Ruang Server', 'Ruang Server IT', 'Gedung A Basement', 'Gedung A', 'Basement', 'Server Room'),
(UUID(), 'Ruang Rektorat', 'Ruang Rektor dan Wakil Rektor', 'Gedung A Lt. 3', 'Gedung A', 'Lantai 3', 'Rektorat'),
(UUID(), 'Ruang Dekan', 'Ruang Dekan Fakultas', 'Gedung B Lt. 2', 'Gedung B', 'Lantai 2', 'Dekanat'),
(UUID(), 'Perpustakaan', 'Perpustakaan Universitas', 'Gedung D', 'Gedung D', 'Lantai 1-3', NULL),
(UUID(), 'Aula Utama', 'Aula untuk acara besar', 'Gedung E', 'Gedung E', 'Lantai 1', 'Aula'),
(UUID(), 'Parkir Gedung A', 'Area parkir Gedung A', 'Depan Gedung A', NULL, NULL, 'Outdoor');

-- Insert default users
INSERT INTO users (id, name, email, role, status, department, phone) VALUES
(UUID(), 'Dr. Ahmad Susanto', '<EMAIL>', 'Administrator', 'Aktif', 'IT', '081234567890'),
(UUID(), 'Siti Rahayu, M.Kom', '<EMAIL>', 'Manager Aset', 'Aktif', 'Sarana Prasarana', '081234567891'),
(UUID(), 'Budi Santoso', '<EMAIL>', 'Staff', 'Aktif', 'Keuangan', '081234567892'),
(UUID(), 'Rina Wati, S.Kom', '<EMAIL>', 'Staff', 'Aktif', 'IT', '081234567893'),
(UUID(), 'Dedi Kurniawan', '<EMAIL>', 'Staff', 'Aktif', 'Sarana Prasarana', '081234567894'),
(UUID(), 'Maya Sari', '<EMAIL>', 'Viewer', 'Aktif', 'Akademik', '081234567895');

-- Insert sample assets
INSERT INTO assets (id, asset_code, asset_name, category, location, status, purchase_date, value, description) VALUES
(UUID(), 'WG-001', 'Laptop Dell Inspiron 15', 'Komputer', 'Gedung A - Lt 1', 'Aktif', '2024-01-15', 8500000, 'Laptop untuk staff administrasi'),
(UUID(), 'WG-002', 'Meja Kerja Kayu Jati', 'Furniture', 'Gedung A - Lt 1', 'Aktif', '2023-12-10', 2500000, 'Meja kerja untuk ruang direktur'),
(UUID(), 'WG-003', 'Printer HP LaserJet Pro', 'Elektronik', 'Gedung A - Lt 2', 'Aktif', '2024-02-20', 3200000, 'Printer untuk keperluan kantor'),
(UUID(), 'WG-004', 'Kursi Ergonomis Executive', 'Furniture', 'Gedung B - Lt 1', 'Aktif', '2024-01-05', 1800000, 'Kursi kerja ergonomis untuk direktur'),
(UUID(), 'WG-005', 'Server Dell PowerEdge R740', 'Komputer', 'Ruang Server', 'Aktif', '2023-11-30', 45000000, 'Server utama sistem informasi'),
(UUID(), 'WG-006', 'Proyektor Epson EB-X41', 'Elektronik', 'Lab Komputer', 'Aktif', '2024-03-10', 4500000, 'Proyektor untuk presentasi lab'),
(UUID(), 'WG-007', 'AC Split Daikin 1.5 PK', 'Elektronik', 'Ruang Rektorat', 'Aktif', '2023-08-15', 3800000, 'AC untuk ruang rektorat'),
(UUID(), 'WG-008', 'Komputer Desktop HP EliteDesk', 'Komputer', 'Lab Komputer', 'Aktif', '2024-01-20', 7200000, 'PC untuk laboratorium komputer'),
(UUID(), 'WG-009', 'Lemari Arsip Besi 4 Pintu', 'Furniture', 'Gedung A - Lt 2', 'Aktif', '2023-09-05', 1200000, 'Lemari untuk penyimpanan dokumen'),
(UUID(), 'WG-010', 'Kamera CCTV Hikvision', 'Elektronik', 'Gedung A - Lt 1', 'Aktif', '2024-02-28', 850000, 'Kamera keamanan lobby gedung A'),
(UUID(), 'WG-011', 'Mobil Toyota Avanza', 'Kendaraan', 'Parkir Gedung A', 'Aktif', '2023-06-12', 185000000, 'Kendaraan operasional universitas'),
(UUID(), 'WG-012', 'Whiteboard Magnetic 120x90', 'Peralatan', 'Lab Bahasa', 'Aktif', '2024-01-08', 450000, 'Papan tulis untuk ruang kelas'),
(UUID(), 'WG-013', 'UPS APC 1500VA', 'Elektronik', 'Ruang Server', 'Aktif', '2023-12-20', 2800000, 'UPS untuk backup power server'),
(UUID(), 'WG-014', 'Scanner Canon DR-C225W', 'Elektronik', 'Gedung B - Lt 2', 'Aktif', '2024-03-15', 3500000, 'Scanner untuk digitalisasi dokumen'),
(UUID(), 'WG-015', 'Meja Meeting Oval 8 Orang', 'Furniture', 'Ruang Dekan', 'Aktif', '2023-10-18', 4200000, 'Meja rapat untuk ruang dekan');

-- Insert application settings
INSERT INTO settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('app_name', 'SIMASET UWG', 'string', 'Nama aplikasi sistem manajemen aset', TRUE),
('app_version', '1.0.0', 'string', 'Versi aplikasi', TRUE),
('organization_name', 'Universitas Widyagama', 'string', 'Nama organisasi', TRUE),
('asset_code_format', 'WG-{000}', 'string', 'Format kode aset', FALSE),
('timezone', 'Asia/Jakarta', 'string', 'Zona waktu sistem', FALSE),
('language', 'id', 'string', 'Bahasa default sistem', TRUE),
('backup_retention_days', '30', 'number', 'Lama penyimpanan backup dalam hari', FALSE),
('auto_backup_enabled', 'true', 'boolean', 'Status backup otomatis', FALSE),
('backup_schedule', 'daily', 'string', 'Jadwal backup otomatis', FALSE),
('max_file_upload_size', '10485760', 'number', 'Ukuran maksimal upload file dalam bytes (10MB)', FALSE),
('allowed_file_types', '["pdf","doc","docx","xls","xlsx","jpg","jpeg","png","gif"]', 'json', 'Tipe file yang diizinkan untuk upload', FALSE),
('maintenance_mode', 'false', 'boolean', 'Status mode maintenance', FALSE),
('email_notifications', 'true', 'boolean', 'Status notifikasi email', FALSE),
('asset_depreciation_rate', '10', 'number', 'Persentase depresiasi aset per tahun', FALSE),
('currency_symbol', 'Rp', 'string', 'Simbol mata uang', TRUE);
