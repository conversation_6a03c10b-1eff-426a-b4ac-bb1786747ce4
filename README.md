# SIMASET UWG - Sistem Manajemen Aset

Sistem Manajemen Aset Universitas Widyagama yang dibangun dengan React, TypeScript, dan mendukung database MySQL dan Supabase.

## 🚀 Fitur Utama

- **Manajemen Aset**: CRUD lengkap untuk data aset
- **Kategori & Lokasi**: Organisasi aset berdasarkan kategori dan lokasi
- **Pencarian & Filter**: Pencarian cepat dan filter lanjutan
- **Backup & Restore**: Backup otomatis dan manual
- **Migrasi Database**: Migrasi mudah dari Supabase ke MySQL
- **User Management**: Manajemen pengguna dan permissions
- **Document Management**: Upload dan kelola dokumen aset
- **Reporting**: Laporan dan analytics aset

## 🗄️ Database Support

### Supabase (Default)
- PostgreSQL cloud database
- Real-time capabilities
- Built-in authentication
- Automatic scaling

### MySQL (Recommended)
- Local database control
- Better performance
- Cost effective
- Full customization

**Project URL**: https://lovable.dev/projects/2281ce74-3a76-4f99-a0be-5bd9ae04b0d7

## 📦 Instalasi

### 1. Clone Repository
```bash
git clone <repository-url>
cd simaset_uwg10
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Setup Environment
```bash
# Copy environment template
cp .env.example .env

# Edit konfigurasi database di .env
nano .env
```

### 4. Pilih Database

#### Option A: Menggunakan Supabase (Default)
```bash
# Konfigurasi Supabase di .env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_key

# Jalankan aplikasi
npm run dev
```

#### Option B: Migrasi ke MySQL (Recommended)
```bash
# 1. Install MySQL
# Windows: Download dari https://dev.mysql.com/downloads/mysql/
# macOS: brew install mysql
# Ubuntu: sudo apt install mysql-server

# 2. Setup MySQL database
npm run setup:mysql

# 3. Konfigurasi MySQL di .env
VITE_MYSQL_HOST=localhost
VITE_MYSQL_PORT=3306
VITE_MYSQL_DATABASE=simaset_db
VITE_MYSQL_USERNAME=root
VITE_MYSQL_PASSWORD=your_password

# 4. Jalankan aplikasi
npm run dev

# 5. Migrasi data via UI
# Buka http://localhost:8080
# Masuk ke Settings > Tab Migrasi
# Klik "Mulai Migrasi"
```

## 🔄 Migrasi Database

### Mengapa Migrasi ke MySQL?
- **Kontrol Penuh**: Database lokal di infrastruktur sendiri
- **Performa**: Akses langsung tanpa latency network
- **Biaya**: Tidak ada subscription fee
- **Keamanan**: Data tersimpan lokal
- **Customization**: Dapat disesuaikan kebutuhan

### Langkah Migrasi:
1. **Setup MySQL**: `npm run setup:mysql`
2. **Konfigurasi .env**: Update MySQL credentials
3. **Test Koneksi**: Settings > MySQL > Test Koneksi
4. **Migrasi Data**: Settings > Migrasi > Mulai Migrasi
5. **Verifikasi**: Cek data di MySQL dan test aplikasi

### Dokumentasi Lengkap:
- [Environment Setup](./ENVIRONMENT_SETUP.md)
- [Migration Guide](./MIGRATION_GUIDE.md)

## 🛠️ Development

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
npm run setup:mysql  # Setup MySQL database
```

### Tech Stack
- **Frontend**: React 18, TypeScript, Vite
- **UI**: ShadCN UI, Tailwind CSS, Radix UI
- **Database**: MySQL, Supabase (PostgreSQL)
- **State**: TanStack Query, React Hook Form
- **Icons**: Lucide React

## 📁 Project Structure
```
src/
├── components/          # Reusable UI components
├── pages/              # Page components
├── lib/                # Utilities and configurations
│   ├── database-adapter.ts    # Database abstraction layer
│   ├── mysql-client.ts        # MySQL client
│   ├── migration-service.ts   # Migration utilities
│   └── env-config.ts          # Environment configuration
├── integrations/       # External service integrations
├── hooks/              # Custom React hooks
└── data/               # Static data and types

mysql-migrations/       # MySQL migration scripts
scripts/               # Setup and utility scripts
```

## 🔧 Configuration

### Environment Variables
```env
# Application
VITE_APP_NAME=SIMASET UWG
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# MySQL Database
VITE_MYSQL_HOST=localhost
VITE_MYSQL_PORT=3306
VITE_MYSQL_DATABASE=simaset_db
VITE_MYSQL_USERNAME=root
VITE_MYSQL_PASSWORD=

# Supabase (Optional)
VITE_SUPABASE_URL=
VITE_SUPABASE_ANON_KEY=

# Security
VITE_ENCRYPTION_KEY=
VITE_JWT_SECRET=
```

## 📊 Database Schema

### Core Tables
- **assets**: Data aset organisasi
- **categories**: Kategori aset
- **locations**: Lokasi penempatan aset
- **users**: Data pengguna sistem
- **documents**: Dokumen terkait aset
- **asset_history**: Audit trail perubahan
- **settings**: Konfigurasi aplikasi

## 🚀 Deployment

### Production Build
```bash
npm run build
```

### Environment Setup
1. Setup production MySQL server
2. Update .env dengan production credentials
3. Run migrations: `npm run setup:mysql`
4. Deploy built files to web server

## 📝 How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/2281ce74-3a76-4f99-a0be-5bd9ae04b0d7) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/2281ce74-3a76-4f99-a0be-5bd9ae04b0d7) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
