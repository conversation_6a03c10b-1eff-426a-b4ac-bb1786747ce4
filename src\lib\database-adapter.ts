// Database Adapter for SIMASET UWG
// This adapter provides a unified interface for both Supabase and MySQL

import { supabase } from '@/integrations/supabase/client';
import { mysqlClient } from './mysql-client';
import { getMySQLConfig } from './env-config';

// Database provider type
export type DatabaseProvider = 'supabase' | 'mysql';

// Get current database provider from environment
export const getCurrentProvider = (): DatabaseProvider => {
  const mysqlConfig = getMySQLConfig();
  // Use MySQL if database name is configured
  return mysqlConfig.database ? 'mysql' : 'supabase';
};

// Asset interface
export interface Asset {
  id?: string;
  asset_code: string;
  asset_name: string;
  category: string;
  location: string;
  status: string;
  purchase_date?: string | null;
  value?: number | null;
  description?: string | null;
  created_at?: string;
  updated_at?: string;
}

// Category interface
export interface Category {
  id?: string;
  name: string;
  description?: string | null;
  icon?: string;
  color?: string;
  created_at?: string;
}

// Location interface
export interface Location {
  id?: string;
  name: string;
  description?: string | null;
  address?: string | null;
  building?: string | null;
  floor?: string | null;
  room?: string | null;
  created_at?: string;
}

// User interface
export interface User {
  id?: string;
  name: string;
  email: string;
  role: string;
  status: string;
  department?: string | null;
  phone?: string | null;
  last_login?: string | null;
  created_at?: string;
  updated_at?: string;
}

// Database response interface
export interface DatabaseResponse<T> {
  data: T | null;
  error: Error | null;
  count?: number;
}

// Assets operations
export const assetsDB = {
  async getAll(): Promise<DatabaseResponse<Asset[]>> {
    try {
      const provider = getCurrentProvider();
      
      if (provider === 'mysql') {
        const data = await mysqlClient.assets.getAll();
        return { data, error: null };
      } else {
        const { data, error } = await supabase
          .from('assets')
          .select('*')
          .order('created_at', { ascending: false });
        return { data, error };
      }
    } catch (error) {
      return { data: null, error: error as Error };
    }
  },

  async getById(id: string): Promise<DatabaseResponse<Asset>> {
    try {
      const provider = getCurrentProvider();
      
      if (provider === 'mysql') {
        const data = await mysqlClient.assets.getById(id);
        return { data, error: null };
      } else {
        const { data, error } = await supabase
          .from('assets')
          .select('*')
          .eq('id', id)
          .single();
        return { data, error };
      }
    } catch (error) {
      return { data: null, error: error as Error };
    }
  },

  async create(asset: Omit<Asset, 'id' | 'created_at' | 'updated_at'>): Promise<DatabaseResponse<Asset>> {
    try {
      const provider = getCurrentProvider();
      
      if (provider === 'mysql') {
        const data = await mysqlClient.assets.create(asset);
        return { data, error: null };
      } else {
        const { data, error } = await supabase
          .from('assets')
          .insert(asset)
          .select()
          .single();
        return { data, error };
      }
    } catch (error) {
      return { data: null, error: error as Error };
    }
  },

  async update(id: string, asset: Partial<Asset>): Promise<DatabaseResponse<Asset>> {
    try {
      const provider = getCurrentProvider();
      
      if (provider === 'mysql') {
        const data = await mysqlClient.assets.update(id, asset);
        return { data, error: null };
      } else {
        const { data, error } = await supabase
          .from('assets')
          .update(asset)
          .eq('id', id)
          .select()
          .single();
        return { data, error };
      }
    } catch (error) {
      return { data: null, error: error as Error };
    }
  },

  async delete(id: string): Promise<DatabaseResponse<null>> {
    try {
      const provider = getCurrentProvider();
      
      if (provider === 'mysql') {
        await mysqlClient.assets.delete(id);
        return { data: null, error: null };
      } else {
        const { error } = await supabase
          .from('assets')
          .delete()
          .eq('id', id);
        return { data: null, error };
      }
    } catch (error) {
      return { data: null, error: error as Error };
    }
  },

  async search(query: string): Promise<DatabaseResponse<Asset[]>> {
    try {
      const provider = getCurrentProvider();
      
      if (provider === 'mysql') {
        const data = await mysqlClient.assets.search(query);
        return { data, error: null };
      } else {
        const { data, error } = await supabase
          .from('assets')
          .select('*')
          .or(`asset_name.ilike.%${query}%,asset_code.ilike.%${query}%,description.ilike.%${query}%`)
          .order('created_at', { ascending: false });
        return { data, error };
      }
    } catch (error) {
      return { data: null, error: error as Error };
    }
  },
};

// Categories operations
export const categoriesDB = {
  async getAll(): Promise<DatabaseResponse<Category[]>> {
    try {
      const provider = getCurrentProvider();
      
      if (provider === 'mysql') {
        const data = await mysqlClient.categories.getAll();
        return { data, error: null };
      } else {
        const { data, error } = await supabase
          .from('categories')
          .select('*')
          .order('name');
        return { data, error };
      }
    } catch (error) {
      return { data: null, error: error as Error };
    }
  },

  async create(category: Omit<Category, 'id' | 'created_at'>): Promise<DatabaseResponse<Category>> {
    try {
      const provider = getCurrentProvider();
      
      if (provider === 'mysql') {
        const data = await mysqlClient.categories.create(category);
        return { data, error: null };
      } else {
        const { data, error } = await supabase
          .from('categories')
          .insert(category)
          .select()
          .single();
        return { data, error };
      }
    } catch (error) {
      return { data: null, error: error as Error };
    }
  },
};

// Locations operations
export const locationsDB = {
  async getAll(): Promise<DatabaseResponse<Location[]>> {
    try {
      const provider = getCurrentProvider();
      
      if (provider === 'mysql') {
        const data = await mysqlClient.locations.getAll();
        return { data, error: null };
      } else {
        const { data, error } = await supabase
          .from('locations')
          .select('*')
          .order('name');
        return { data, error };
      }
    } catch (error) {
      return { data: null, error: error as Error };
    }
  },

  async create(location: Omit<Location, 'id' | 'created_at'>): Promise<DatabaseResponse<Location>> {
    try {
      const provider = getCurrentProvider();
      
      if (provider === 'mysql') {
        const data = await mysqlClient.locations.create(location);
        return { data, error: null };
      } else {
        const { data, error } = await supabase
          .from('locations')
          .insert(location)
          .select()
          .single();
        return { data, error };
      }
    } catch (error) {
      return { data: null, error: error as Error };
    }
  },
};

// Database operations
export const databaseDB = {
  async testConnection(): Promise<DatabaseResponse<any>> {
    try {
      const provider = getCurrentProvider();
      
      if (provider === 'mysql') {
        const data = await mysqlClient.database.testConnection();
        return { data, error: null };
      } else {
        const { data, error } = await supabase
          .from('assets')
          .select('count(*)', { count: 'exact', head: true });
        return { data: { connected: !error }, error };
      }
    } catch (error) {
      return { data: null, error: error as Error };
    }
  },

  async getStats(): Promise<DatabaseResponse<any>> {
    try {
      const provider = getCurrentProvider();
      
      if (provider === 'mysql') {
        const data = await mysqlClient.database.getStats();
        return { data, error: null };
      } else {
        // Get counts from Supabase
        const [assetsResult, categoriesResult, locationsResult] = await Promise.all([
          supabase.from('assets').select('*', { count: 'exact', head: true }),
          supabase.from('categories').select('*', { count: 'exact', head: true }),
          supabase.from('locations').select('*', { count: 'exact', head: true }),
        ]);

        const data = {
          assets_count: assetsResult.count || 0,
          categories_count: categoriesResult.count || 0,
          locations_count: locationsResult.count || 0,
        };

        return { data, error: null };
      }
    } catch (error) {
      return { data: null, error: error as Error };
    }
  },
};

// Export unified database client
export const db = {
  assets: assetsDB,
  categories: categoriesDB,
  locations: locationsDB,
  database: databaseDB,
  getCurrentProvider,
};
