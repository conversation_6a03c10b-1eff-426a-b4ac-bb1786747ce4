import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Building2, 
  Plus, 
  Search, 
  Filter,
  BarChart3,
  MapPin,
  Package,
  Settings
} from 'lucide-react';
import AssetTable from '../components/AssetTable';
import AssetForm from '../components/AssetForm';

const Assets = () => {
  const [searchTerm, setSearchTerm] = useState('');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            Manajemen Aset
          </h1>
          <p className="text-gray-600 mt-2"><PERSON><PERSON><PERSON> semua aset properti universitas</p>
        </div>
        <AssetForm onSuccess={() => window.location.reload()} />
      </div>

      {/* Tabs for different asset management functions */}
      <Tabs defaultValue="assets" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="assets">Daftar Aset</TabsTrigger>
          <TabsTrigger value="categories">Kategori</TabsTrigger>
          <TabsTrigger value="locations">Lokasi</TabsTrigger>
          <TabsTrigger value="maintenance">Pemeliharaan</TabsTrigger>
          <TabsTrigger value="reports">Laporan Aset</TabsTrigger>
        </TabsList>

        <TabsContent value="assets" className="space-y-6">
          {/* Search and Filter */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div className="flex-1 w-full sm:w-auto">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Cari aset berdasarkan nama, kode, atau lokasi..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={() => console.log('Filter assets')}>
                    <Filter className="w-4 h-4 mr-2" />
                    Filter
                  </Button>
                  <Button variant="outline" onClick={() => window.location.assign('/reports')}>
                    <BarChart3 className="w-4 h-4 mr-2" />
                    Analisis
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Asset Table */}
          <Card>
            <CardHeader>
              <CardTitle>Data Aset Properti</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <AssetTable />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Kategori Aset</CardTitle>
              <Button onClick={() => console.log('Add category')}>
                <Plus className="w-4 h-4 mr-2" />
                Tambah Kategori
              </Button>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={() => console.log('View Bangunan category')}>
                  <div className="flex items-center gap-3">
                    <Building2 className="w-8 h-8 text-blue-600" />
                    <div>
                      <h3 className="font-semibold">Bangunan</h3>
                      <p className="text-sm text-gray-600">45 aset</p>
                    </div>
                  </div>
                </Card>
                <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={() => console.log('View Peralatan category')}>
                  <div className="flex items-center gap-3">
                    <Package className="w-8 h-8 text-green-600" />
                    <div>
                      <h3 className="font-semibold">Peralatan</h3>
                      <p className="text-sm text-gray-600">128 aset</p>
                    </div>
                  </div>
                </Card>
                <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={() => console.log('View Teknologi category')}>
                  <div className="flex items-center gap-3">
                    <Settings className="w-8 h-8 text-purple-600" />
                    <div>
                      <h3 className="font-semibold">Teknologi</h3>
                      <p className="text-sm text-gray-600">67 aset</p>
                    </div>
                  </div>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="locations" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Lokasi Aset</CardTitle>
              <Button onClick={() => console.log('Add location')}>
                <Plus className="w-4 h-4 mr-2" />
                Tambah Lokasi
              </Button>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={() => console.log('View Gedung Rektorat location')}>
                  <div className="flex items-center gap-3">
                    <MapPin className="w-8 h-8 text-red-600" />
                    <div>
                      <h3 className="font-semibold">Gedung Rektorat</h3>
                      <p className="text-sm text-gray-600">25 aset</p>
                    </div>
                  </div>
                </Card>
                <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={() => console.log('View Fakultas Teknik location')}>
                  <div className="flex items-center gap-3">
                    <MapPin className="w-8 h-8 text-blue-600" />
                    <div>
                      <h3 className="font-semibold">Fakultas Teknik</h3>
                      <p className="text-sm text-gray-600">89 aset</p>
                    </div>
                  </div>
                </Card>
                <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={() => console.log('View Perpustakaan location')}>
                  <div className="flex items-center gap-3">
                    <MapPin className="w-8 h-8 text-green-600" />
                    <div>
                      <h3 className="font-semibold">Perpustakaan</h3>
                      <p className="text-sm text-gray-600">34 aset</p>
                    </div>
                  </div>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Jadwal Pemeliharaan</CardTitle>
              <Button onClick={() => console.log('Add maintenance schedule')}>
                <Plus className="w-4 h-4 mr-2" />
                Tambah Jadwal
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card className="p-4 border-l-4 border-yellow-500">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold text-yellow-700">Pemeliharaan Rutin</h3>
                        <p className="text-sm text-gray-600">Setiap 3 bulan</p>
                        <p className="text-xs text-gray-500">15 aset terjadwal</p>
                      </div>
                      <Button variant="outline" size="sm" onClick={() => console.log('View routine maintenance')}>
                        <Settings className="w-4 h-4" />
                      </Button>
                    </div>
                  </Card>
                  <Card className="p-4 border-l-4 border-red-500">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold text-red-700">Pemeliharaan Urgent</h3>
                        <p className="text-sm text-gray-600">Perlu segera</p>
                        <p className="text-xs text-gray-500">3 aset memerlukan</p>
                      </div>
                      <Button variant="outline" size="sm" onClick={() => console.log('View urgent maintenance')}>
                        <Settings className="w-4 h-4" />
                      </Button>
                    </div>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Laporan Aset</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button variant="outline" className="h-20 flex-col" onClick={() => window.location.assign('/reports')}>
                  <BarChart3 className="w-8 h-8 mb-2" />
                  Laporan Nilai Aset
                </Button>
                <Button variant="outline" className="h-20 flex-col" onClick={() => window.location.assign('/reports')}>
                  <Building2 className="w-8 h-8 mb-2" />
                  Laporan per Lokasi
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Assets;