import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Filter, 
  Download, 
  TrendingUp, 
  Building2, 
  MapPin, 
  DollarSign,
  BarChart3
} from 'lucide-react';
import AssetTable from '../components/AssetTable';
import AssetForm from '../components/AssetForm';

const Index = () => {
  const [searchTerm, setSearchTerm] = useState('');
  
  const handleExport = () => {
    console.log('Exporting data...');
  };

  const handleFilter = () => {
    console.log('Opening filter dialog...');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            Dashboard Manajemen Aset
          </h1>
          <p className="text-gray-600 mt-2">Universitas Widyagama Malang</p>
        </div>
        <AssetForm onSuccess={() => window.location.reload()} />
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium">Total Aset</p>
                <p className="text-2xl font-bold text-blue-900">1,234</p>
                <p className="text-blue-600 text-xs">+12% dari bulan lalu</p>
              </div>
              <Button 
                variant="ghost" 
                className="bg-blue-600 p-3 rounded-full hover:bg-blue-700"
                onClick={() => window.location.assign('/assets')}
              >
                <Building2 className="w-6 h-6 text-white" />
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">Nilai Total</p>
                <p className="text-2xl font-bold text-green-900">Rp 45.2M</p>
                <p className="text-green-600 text-xs">+8% dari bulan lalu</p>
              </div>
              <Button 
                variant="ghost" 
                className="bg-green-600 p-3 rounded-full hover:bg-green-700"
                onClick={() => window.location.assign('/reports')}
              >
                <DollarSign className="w-6 h-6 text-white" />
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 text-sm font-medium">Lokasi</p>
                <p className="text-2xl font-bold text-purple-900">15</p>
                <p className="text-purple-600 text-xs">3 gedung utama</p>
              </div>
              <Button 
                variant="ghost" 
                className="bg-purple-600 p-3 rounded-full hover:bg-purple-700"
                onClick={() => window.location.assign('/assets')}
              >
                <MapPin className="w-6 h-6 text-white" />
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-600 text-sm font-medium">Pertumbuhan</p>
                <p className="text-2xl font-bold text-orange-900">+23.5%</p>
                <p className="text-orange-600 text-xs">Target tercapai</p>
              </div>
              <Button 
                variant="ghost" 
                className="bg-orange-600 p-3 rounded-full hover:bg-orange-700"
                onClick={() => window.location.assign('/reports')}
              >
                <TrendingUp className="w-6 h-6 text-white" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter Bar */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="flex-1 w-full sm:w-auto">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Cari aset berdasarkan nama, kode, atau lokasi..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={handleFilter}
                className="flex items-center gap-2"
              >
                <Filter className="w-4 h-4" />
                Filter
              </Button>
              <Button 
                variant="outline" 
                onClick={handleExport}
                className="flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                Export
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Asset Table */}
      <Card>
        <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 border-b">
          <CardTitle className="text-xl font-semibold text-gray-800">
            Data Aset Properti
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <AssetTable />
        </CardContent>
      </Card>
    </div>
  );
};

export default Index;