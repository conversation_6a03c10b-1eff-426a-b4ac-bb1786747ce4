import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  BarChart3, 
  FileText, 
  Download, 
  Filter,
  Send,
  Calendar as CalendarIcon,
  TrendingUp,
  Building2,
  DollarSign,
  MapPin,
  Printer,
  Mail,
  Share2
} from 'lucide-react';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';

const Reports = () => {
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();
  const [selectedReport, setSelectedReport] = useState('');

  const reportTypes = [
    {
      id: 'asset-value',
      title: 'Laporan Nilai Aset',
      description: 'Laporan total nilai aset berdasarkan kategori dan lokasi',
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      id: 'asset-location',
      title: 'Laporan Aset per Lokasi',
      description: 'Distribusi aset berdasarkan lokasi dan gedung',
      icon: MapPin,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      id: 'asset-category',
      title: 'Laporan per Kategori',
      description: 'Pengelompokan aset berdasarkan kategori',
      icon: Building2,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      id: 'asset-trend',
      title: 'Laporan Trend Aset',
      description: 'Analisis pertumbuhan dan perubahan aset',
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    }
  ];

  const generateReport = () => {
    console.log('Generating report...', {
      type: selectedReport,
      startDate,
      endDate
    });
  };

  const exportReport = (format: string) => {
    console.log(`Exporting report as ${format}...`);
  };

  const printReport = () => {
    window.print();
  };

  const exportPDF = () => {
    console.log('Exporting report as pdf...');
  };

  const exportExcel = () => {
    console.log('Exporting report as excel...');
  };

  const sendEmail = () => {
    console.log('Sending report via email...');
  };

  const sampleReportData = [
    { category: 'Bangunan', count: 45, value: 'Rp 25.2M', percentage: 35 },
    { category: 'Peralatan', count: 128, value: 'Rp 15.8M', percentage: 28 },
    { category: 'Teknologi', count: 67, value: 'Rp 12.4M', percentage: 22 },
    { category: 'Kendaraan', count: 15, value: 'Rp 8.6M', percentage: 15 }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            Laporan Aset
          </h1>
          <p className="text-gray-600 mt-2">Buat dan kelola laporan aset properti</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={printReport}>
            <Printer className="w-4 h-4 mr-2" />
            Print
          </Button>
          <Button onClick={generateReport}>
            <FileText className="w-4 h-4 mr-2" />
            Buat Laporan
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="generate" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="generate">Buat Laporan</TabsTrigger>
          <TabsTrigger value="templates">Template</TabsTrigger>
          <TabsTrigger value="scheduled">Terjadwal</TabsTrigger>
          <TabsTrigger value="history">Riwayat</TabsTrigger>
        </TabsList>

        <TabsContent value="generate" className="space-y-6">
          {/* Report Generator */}
          <Card>
            <CardHeader>
              <CardTitle>Generator Laporan</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Report Type Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {reportTypes.map((report) => {
                  const Icon = report.icon;
                  return (
                    <Card 
                      key={report.id} 
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        selectedReport === report.id ? 'ring-2 ring-blue-500' : ''
                      }`}
                      onClick={() => setSelectedReport(report.id)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                          <div className={`p-2 rounded-lg ${report.bgColor}`}>
                            <Icon className={`w-6 h-6 ${report.color}`} />
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold">{report.title}</h3>
                            <p className="text-sm text-gray-600 mt-1">{report.description}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>

              {/* Filters */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Periode Mulai</label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start">
                        <CalendarIcon className="w-4 h-4 mr-2" />
                        {startDate ? format(startDate, 'dd MMM yyyy', { locale: id }) : 'Pilih tanggal'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={startDate}
                        onSelect={setStartDate}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Periode Selesai</label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start">
                        <CalendarIcon className="w-4 h-4 mr-2" />
                        {endDate ? format(endDate, 'dd MMM yyyy', { locale: id }) : 'Pilih tanggal'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={endDate}
                        onSelect={setEndDate}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Lokasi</label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Semua lokasi" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Semua Lokasi</SelectItem>
                      <SelectItem value="rektorat">Gedung Rektorat</SelectItem>
                      <SelectItem value="teknik">Fakultas Teknik</SelectItem>
                      <SelectItem value="perpus">Perpustakaan</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Generate Button */}
              <div className="flex gap-2">
                <Button onClick={generateReport} className="flex-1">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Generate Laporan
                </Button>
                <Button variant="outline" onClick={exportPDF}>
                  <Download className="w-4 h-4 mr-2" />
                  Export PDF
                </Button>
                <Button variant="outline" onClick={exportExcel}>
                  <Download className="w-4 h-4 mr-2" />
                  Export Excel
                </Button>
                <Button variant="outline" onClick={sendEmail}>
                  <Send className="w-4 h-4 mr-2" />
                  Kirim Email
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Sample Report Preview */}
          {selectedReport && (
            <Card>
              <CardHeader>
                <CardTitle>Preview Laporan</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <h2 className="text-xl font-bold">Laporan Aset per Kategori</h2>
                    <p className="text-gray-600">Periode: Januari 2024 - Desember 2024</p>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse border border-gray-300">
                      <thead>
                        <tr className="bg-gray-50">
                          <th className="border border-gray-300 p-3 text-left">Kategori</th>
                          <th className="border border-gray-300 p-3 text-center">Jumlah</th>
                          <th className="border border-gray-300 p-3 text-right">Nilai Total</th>
                          <th className="border border-gray-300 p-3 text-center">Persentase</th>
                        </tr>
                      </thead>
                      <tbody>
                        {sampleReportData.map((item, index) => (
                          <tr key={index}>
                            <td className="border border-gray-300 p-3">{item.category}</td>
                            <td className="border border-gray-300 p-3 text-center">{item.count}</td>
                            <td className="border border-gray-300 p-3 text-right">{item.value}</td>
                            <td className="border border-gray-300 p-3 text-center">{item.percentage}%</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Template Laporan</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <FileText className="w-6 h-6 text-blue-600" />
                    <h3 className="font-semibold">Laporan Bulanan</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">Template laporan bulanan aset</p>
                  <Button size="sm" className="w-full" onClick={() => console.log('Using monthly template')}>Gunakan Template</Button>
                </Card>
                <Card className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <BarChart3 className="w-6 h-6 text-green-600" />
                    <h3 className="font-semibold">Laporan Tahunan</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">Template laporan tahunan komprehensif</p>
                  <Button size="sm" className="w-full" onClick={() => console.log('Using yearly template')}>Gunakan Template</Button>
                </Card>
                <Card className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <TrendingUp className="w-6 h-6 text-purple-600" />
                    <h3 className="font-semibold">Laporan Audit</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">Template untuk audit aset</p>
                  <Button size="sm" className="w-full" onClick={() => console.log('Using audit template')}>Gunakan Template</Button>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scheduled" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Laporan Terjadwal</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-semibold">Laporan Bulanan Aset</h3>
                    <p className="text-sm text-gray-600">Setiap tanggal 1, <NAME_EMAIL></p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge>Aktif</Badge>
                    <Button variant="outline" size="sm" onClick={() => console.log('Edit scheduled report')}>Edit</Button>
                  </div>
                </div>
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-semibold">Laporan Mingguan Lokasi</h3>
                    <p className="text-sm text-gray-600">Setiap Senin, <NAME_EMAIL></p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">Nonaktif</Badge>
                    <Button variant="outline" size="sm" onClick={() => console.log('Edit weekly report')}>Edit</Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Riwayat Laporan</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-semibold">Laporan Aset Januari 2024</h3>
                    <p className="text-sm text-gray-600">Dibuat: 01 Feb 2024, 09:30 WIB</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={() => console.log('Download January 2024 report')}>
                      <Download className="w-4 h-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => console.log('Share January 2024 report')}>
                      <Share2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-semibold">Laporan Nilai Aset Q4 2023</h3>
                    <p className="text-sm text-gray-600">Dibuat: 15 Jan 2024, 14:20 WIB</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={() => console.log('Download Q4 2023 report')}>
                      <Download className="w-4 h-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => console.log('Share Q4 2023 report')}>
                      <Share2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Reports;