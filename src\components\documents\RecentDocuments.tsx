import { Button } from '@/components/ui/button';
import { FileText, Eye } from 'lucide-react';

interface Document {
  id: number;
  name: string;
  uploadDate: string;
  uploadedBy: string;
}

interface RecentDocumentsProps {
  documents: Document[];
}

const RecentDocuments = ({ documents }: RecentDocumentsProps) => {
  return (
    <div className="space-y-3">
      {documents.slice(0, 5).map((doc) => (
        <div key={doc.id} className="flex items-center gap-4 p-3 border rounded-lg">
          <div className="p-2 bg-blue-100 rounded">
            <FileText className="w-4 h-4 text-blue-600" />
          </div>
          <div className="flex-1">
            <p className="font-medium">{doc.name}</p>
            <p className="text-sm text-gray-600">Upload {doc.uploadDate} oleh {doc.uploadedBy}</p>
          </div>
          <Button variant="outline" size="sm" onClick={() => console.log('View recent document:', doc.id)}>
            <Eye className="w-4 h-4" />
          </Button>
        </div>
      ))}
    </div>
  );
};

export default RecentDocuments;