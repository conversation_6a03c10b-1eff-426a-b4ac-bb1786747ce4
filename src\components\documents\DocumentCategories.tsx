import { Card } from '@/components/ui/card';
import { FileText, File, Image, Archive } from 'lucide-react';

interface Category {
  name: string;
  count: number;
  icon: typeof FileText;
  color: string;
}

interface DocumentCategoriesProps {
  categories: Category[];
}

const DocumentCategories = ({ categories }: DocumentCategoriesProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {categories.map((category, index) => {
        const Icon = category.icon;
        return (
          <Card key={index} className="p-4 hover:shadow-md transition-shadow cursor-pointer">
            <div className="flex items-center gap-3">
              <Icon className={`w-8 h-8 ${category.color}`} />
              <div>
                <h3 className="font-semibold">{category.name}</h3>
                <p className="text-sm text-gray-600">{category.count} dokumen</p>
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
};

export default DocumentCategories;