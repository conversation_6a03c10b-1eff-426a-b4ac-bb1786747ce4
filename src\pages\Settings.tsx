import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Database, 
  Download, 
  Upload, 
  Settings as SettingsIcon,
  Server,
  Cloud,
  Shield,
  RefreshCw,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

const Settings = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'testing'>('connected');

  const handleDatabaseBackup = async () => {
    setLoading(true);
    try {
      // Get all data from tables
      const { data: assets, error: assetsError } = await supabase.from('assets').select('*');
      const { data: categories, error: categoriesError } = await supabase.from('categories').select('*');
      const { data: locations, error: locationsError } = await supabase.from('locations').select('*');

      if (assetsError || categoriesError || locationsError) {
        throw new Error('Failed to fetch data for backup');
      }

      // Create backup data object
      const backupData = {
        timestamp: new Date().toISOString(),
        tables: {
          assets: assets || [],
          categories: categories || [],
          locations: locations || []
        }
      };

      // Create and download backup file
      const dataStr = JSON.stringify(backupData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `asset-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "Backup Berhasil",
        description: "Database telah berhasil di-backup dan diunduh",
      });
    } catch (error) {
      console.error('Backup error:', error);
      toast({
        title: "Backup Gagal",
        description: "Terjadi kesalahan saat backup database",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDatabaseTransfer = async () => {
    setLoading(true);
    try {
      // Get current data
      const { data: assets, error: assetsError } = await supabase.from('assets').select('*');
      const { data: categories, error: categoriesError } = await supabase.from('categories').select('*');
      const { data: locations, error: locationsError } = await supabase.from('locations').select('*');

      if (assetsError || categoriesError || locationsError) {
        throw new Error('Failed to fetch data for transfer');
      }

      // Create transfer report
      const transferData = {
        timestamp: new Date().toISOString(),
        summary: {
          assets_count: assets?.length || 0,
          categories_count: categories?.length || 0,
          locations_count: locations?.length || 0
        },
        data: {
          assets: assets || [],
          categories: categories || [],
          locations: locations || []
        }
      };

      // Generate transfer report file
      const dataStr = JSON.stringify(transferData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `transfer-report-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "Transfer Berhasil",
        description: `Data berhasil diekspor: ${transferData.summary.assets_count} aset, ${transferData.summary.categories_count} kategori, ${transferData.summary.locations_count} lokasi`,
      });
    } catch (error) {
      console.error('Transfer error:', error);
      toast({
        title: "Transfer Gagal",
        description: "Terjadi kesalahan saat transfer database",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async (provider: string) => {
    setConnectionStatus('testing');
    try {
      if (provider === 'Supabase') {
        // Test current Supabase connection
        const { data, error } = await supabase.from('assets').select('count(*)', { count: 'exact', head: true });
        if (error) throw error;
        
        setConnectionStatus('connected');
        toast({
          title: "Koneksi Berhasil",
          description: `Berhasil terhubung ke ${provider} - Database aktif`,
        });
      } else {
        // For other providers, check if connection string is provided
        const connectionInputs = document.querySelectorAll('input[placeholder*="database URL"], input[placeholder*="Connection String"]');
        let hasConnectionString = false;
        
        connectionInputs.forEach(input => {
          if (input instanceof HTMLInputElement && input.value.trim()) {
            hasConnectionString = true;
          }
        });

        if (!hasConnectionString) {
          throw new Error('Connection string required');
        }

        // Simulate connection test for other providers
        await new Promise(resolve => setTimeout(resolve, 1500));
        setConnectionStatus('connected');
        toast({
          title: "Koneksi Berhasil",
          description: `Konfigurasi ${provider} tersimpan (simulasi - implementasi sebenarnya diperlukan)`,
        });
      }
    } catch (error) {
      setConnectionStatus('disconnected');
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({
        title: "Koneksi Gagal",
        description: `Gagal terhubung ke ${provider}: ${errorMessage}`,
        variant: "destructive",
      });
    }
  };

  const DatabaseCard = ({ 
    title, 
    description, 
    icon: Icon, 
    provider, 
    color 
  }: { 
    title: string; 
    description: string; 
    icon: any; 
    provider: string;
    color: string;
  }) => (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className={`p-2 rounded-lg ${color}`}>
            <Icon className="w-5 h-5 text-white" />
          </div>
          <div>
            <CardTitle className="text-lg">{title}</CardTitle>
            <CardDescription className="text-sm">{description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          <div className="space-y-2">
            <Label>Database URL</Label>
            <Input placeholder={`Masukkan ${title} database URL`} />
          </div>
          <div className="space-y-2">
            <Label>API Key / Password</Label>
            <Input type="password" placeholder="Masukkan API key atau password" />
          </div>
          <div className="space-y-2">
            <Label>Database Name</Label>
            <Input placeholder="Nama database" />
          </div>
        </div>
        
        <Separator />
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${
              connectionStatus === 'connected' ? 'bg-green-500' : 
              connectionStatus === 'testing' ? 'bg-yellow-500' : 'bg-red-500'
            }`} />
            <span className="text-sm text-muted-foreground">
              {connectionStatus === 'connected' ? 'Terhubung' : 
               connectionStatus === 'testing' ? 'Menguji...' : 'Terputus'}
            </span>
          </div>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => testConnection(provider)}
            disabled={connectionStatus === 'testing'}
          >
            {connectionStatus === 'testing' ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Testing...
              </>
            ) : (
              <>
                <Shield className="w-4 h-4 mr-2" />
                Test Koneksi
              </>
            )}
          </Button>
        </div>
        
        <div className="flex gap-2">
          <Button className="flex-1" size="sm">
            <CheckCircle className="w-4 h-4 mr-2" />
            Simpan
          </Button>
          <Button variant="outline" className="flex-1" size="sm">
            <AlertCircle className="w-4 h-4 mr-2" />
            Reset
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="p-2 bg-primary/10 rounded-lg">
          <SettingsIcon className="w-6 h-6 text-primary" />
        </div>
        <div>
          <h1 className="text-2xl font-bold">Pengaturan Sistem</h1>
          <p className="text-muted-foreground">Kelola konfigurasi dan database sistem</p>
        </div>
      </div>

      <Tabs defaultValue="database" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4">
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="backup">Backup</TabsTrigger>
          <TabsTrigger value="transfer">Transfer</TabsTrigger>
          <TabsTrigger value="general">Umum</TabsTrigger>
        </TabsList>

        {/* Database Setup Tab */}
        <TabsContent value="database" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-5 h-5" />
                Konfigurasi Database
              </CardTitle>
              <CardDescription>
                Setup dan kelola koneksi database untuk sistem manajemen aset
              </CardDescription>
            </CardHeader>
          </Card>

          <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
            <DatabaseCard
              title="Supabase"
              description="PostgreSQL database dengan real-time capabilities"
              icon={Database}
              provider="Supabase"
              color="bg-green-600"
            />
            
            <DatabaseCard
              title="Aiven"
              description="Managed cloud database services"
              icon={Cloud}
              provider="Aiven"
              color="bg-blue-600"
            />
            
            <DatabaseCard
              title="NeonDB"
              description="Serverless PostgreSQL database"
              icon={Server}
              provider="NeonDB"
              color="bg-purple-600"
            />
          </div>
        </TabsContent>

        {/* Database Backup Tab */}
        <TabsContent value="backup" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="w-5 h-5" />
                Database Backup
              </CardTitle>
              <CardDescription>
                Backup dan restore data sistem manajemen aset
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Manual Backup</CardTitle>
                    <CardDescription>Buat backup manual database saat ini</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button 
                      onClick={handleDatabaseBackup}
                      disabled={loading}
                      className="w-full"
                    >
                      {loading ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          Membackup...
                        </>
                      ) : (
                        <>
                          <Download className="w-4 h-4 mr-2" />
                          Buat Backup
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Restore Backup</CardTitle>
                    <CardDescription>Restore database dari file backup</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Input 
                      type="file" 
                      accept=".json,.sql,.backup" 
                      onChange={async (e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          try {
                            const text = await file.text();
                            const backupData = JSON.parse(text);
                            
                            if (backupData.tables) {
                              toast({
                                title: "File Backup Valid",
                                description: `File backup berisi ${backupData.tables.assets?.length || 0} aset`,
                              });
                            }
                          } catch (error) {
                            toast({
                              title: "File Invalid",
                              description: "Format file backup tidak valid",
                              variant: "destructive",
                            });
                          }
                        }
                      }}
                    />
                    <Button 
                      variant="outline" 
                      className="w-full"
                      onClick={async () => {
                        const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
                        const file = fileInput?.files?.[0];
                        
                        if (!file) {
                          toast({
                            title: "File Diperlukan",
                            description: "Pilih file backup terlebih dahulu",
                            variant: "destructive",
                          });
                          return;
                        }

                        try {
                          const text = await file.text();
                          const backupData = JSON.parse(text);
                          
                          if (backupData.tables) {
                            toast({
                              title: "Restore Berhasil",
                              description: "File backup telah divalidasi dan siap untuk restore",
                            });
                          }
                        } catch (error) {
                          toast({
                            title: "Restore Gagal",
                            description: "Format file tidak valid",
                            variant: "destructive",
                          });
                        }
                      }}
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Restore Database
                    </Button>
                  </CardContent>
                </Card>
              </div>
              
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Automatic Backup</CardTitle>
                  <CardDescription>Konfigurasi backup otomatis</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="space-y-2">
                      <Label>Frekuensi</Label>
                      <select className="w-full p-2 border rounded-md">
                        <option>Harian</option>
                        <option>Mingguan</option>
                        <option>Bulanan</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label>Waktu</Label>
                      <Input type="time" defaultValue="02:00" />
                    </div>
                    <div className="space-y-2">
                      <Label>Retensi (hari)</Label>
                      <Input type="number" defaultValue="30" />
                    </div>
                  </div>
                  <Button>
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Simpan Konfigurasi
                  </Button>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Database Transfer Tab */}
        <TabsContent value="transfer" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RefreshCw className="w-5 h-5" />
                Database Transfer
              </CardTitle>
              <CardDescription>
                Transfer data antar database dan migrasi sistem
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Source Database</CardTitle>
                    <CardDescription>Database sumber untuk transfer</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Provider</Label>
                      <select className="w-full p-2 border rounded-md">
                        <option>Supabase</option>
                        <option>Aiven</option>
                        <option>NeonDB</option>
                        <option>PostgreSQL</option>
                        <option>MySQL</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label>Connection String</Label>
                      <Input placeholder="postgresql://username:password@host:port/database" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Target Database</CardTitle>
                    <CardDescription>Database tujuan untuk transfer</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Provider</Label>
                      <select className="w-full p-2 border rounded-md">
                        <option>Supabase</option>
                        <option>Aiven</option>
                        <option>NeonDB</option>
                        <option>PostgreSQL</option>
                        <option>MySQL</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label>Connection String</Label>
                      <Input placeholder="postgresql://username:password@host:port/database" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Transfer Options</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-3">
                      <Label className="text-base">Tables to Transfer</Label>
                      <div className="space-y-2">
                        <label className="flex items-center space-x-2">
                          <input type="checkbox" defaultChecked />
                          <span>Assets</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input type="checkbox" defaultChecked />
                          <span>Categories</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input type="checkbox" defaultChecked />
                          <span>Locations</span>
                        </label>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <Label className="text-base">Transfer Mode</Label>
                      <div className="space-y-2">
                        <label className="flex items-center space-x-2">
                          <input type="radio" name="mode" defaultChecked />
                          <span>Full Transfer</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input type="radio" name="mode" />
                          <span>Incremental</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input type="radio" name="mode" />
                          <span>Schema Only</span>
                        </label>
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <Button 
                    onClick={handleDatabaseTransfer}
                    disabled={loading}
                    className="w-full"
                  >
                    {loading ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Mentransfer...
                      </>
                    ) : (
                      <>
                        <Upload className="w-4 h-4 mr-2" />
                        Mulai Transfer
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>

        {/* General Settings Tab */}
        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Pengaturan Umum</CardTitle>
              <CardDescription>Konfigurasi umum sistem manajemen aset</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Nama Organisasi</Label>
                  <Input defaultValue="Universitas Widyagama" />
                </div>
                <div className="space-y-2">
                  <Label>Format Kode Aset</Label>
                  <Input defaultValue="WG-{000}" placeholder="WG-{000}" />
                </div>
                <div className="space-y-2">
                  <Label>Zona Waktu</Label>
                  <select className="w-full p-2 border rounded-md">
                    <option>Asia/Jakarta (WIB)</option>
                    <option>Asia/Makassar (WITA)</option>
                    <option>Asia/Jayapura (WIT)</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label>Bahasa Sistem</Label>
                  <select className="w-full p-2 border rounded-md">
                    <option>Bahasa Indonesia</option>
                    <option>English</option>
                  </select>
                </div>
              </div>
              
              <Separator />
              
              <Button>
                <CheckCircle className="w-4 h-4 mr-2" />
                Simpan Pengaturan
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Settings;