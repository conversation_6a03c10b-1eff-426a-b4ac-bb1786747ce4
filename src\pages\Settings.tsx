import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Database, 
  Download, 
  Upload, 
  Settings as SettingsIcon,
  Server,
  Cloud,
  Shield,
  RefreshCw,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import {
  getMySQLConfig,
  generateMySQLConnectionString,
  validateMySQLConfig,
  getAppConfig,
  getBackupConfig,
  MySQLConfig
} from '@/lib/env-config';
import {
  downloadEnvFile,
  copyEnvToClipboard,
  generateMySQLEnvContent,
  hasConfigChanged
} from '@/lib/env-writer';

const Settings = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'testing'>('connected');

  // MySQL Configuration State - Load from environment variables
  const [mysqlConfig, setMysqlConfig] = useState<MySQLConfig>(() => getMySQLConfig());
  const [showEnvInstructions, setShowEnvInstructions] = useState(false);

  // Load MySQL configuration from environment variables on component mount
  useEffect(() => {
    const envConfig = getMySQLConfig();
    setMysqlConfig(envConfig);
  }, []);

  // Handle MySQL config changes
  const handleMysqlConfigChange = (field: keyof MySQLConfig, value: string) => {
    setMysqlConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Save MySQL configuration to .env file
  const saveMysqlConfig = async () => {
    try {
      // Validate configuration
      const validation = validateMySQLConfig(mysqlConfig);
      if (!validation.isValid) {
        toast({
          title: "Konfigurasi Tidak Valid",
          description: validation.errors.join(', '),
          variant: "destructive",
        });
        return;
      }

      // Check if configuration has changed
      if (!hasConfigChanged(mysqlConfig)) {
        toast({
          title: "Tidak Ada Perubahan",
          description: "Konfigurasi MySQL sama dengan yang ada di environment",
        });
        return;
      }

      // Show instructions for updating .env file
      setShowEnvInstructions(true);

      // Download updated .env file
      downloadEnvFile(mysqlConfig, '.env');

      toast({
        title: "File .env Diunduh",
        description: "Silakan ganti file .env di root project dan restart aplikasi",
      });
    } catch (error) {
      toast({
        title: "Gagal Menyimpan",
        description: "Terjadi kesalahan saat menyimpan konfigurasi",
        variant: "destructive",
      });
    }
  };

  // Reset MySQL configuration to environment defaults
  const resetMysqlConfig = () => {
    const defaultConfig = getMySQLConfig();
    setMysqlConfig(defaultConfig);
    setShowEnvInstructions(false);
    toast({
      title: "Konfigurasi Direset",
      description: "Pengaturan MySQL dikembalikan ke nilai environment",
    });
  };

  // Copy configuration to clipboard
  const copyConfigToClipboard = async () => {
    try {
      const success = await copyEnvToClipboard(mysqlConfig);
      if (success) {
        toast({
          title: "Disalin ke Clipboard",
          description: "Konfigurasi .env berhasil disalin",
        });
      } else {
        throw new Error('Failed to copy');
      }
    } catch (error) {
      toast({
        title: "Gagal Menyalin",
        description: "Tidak dapat menyalin ke clipboard",
        variant: "destructive",
      });
    }
  };

  const handleDatabaseBackup = async () => {
    setLoading(true);
    try {
      // Get all data from tables
      const { data: assets, error: assetsError } = await supabase.from('assets').select('*');
      const { data: categories, error: categoriesError } = await supabase.from('categories').select('*');
      const { data: locations, error: locationsError } = await supabase.from('locations').select('*');

      if (assetsError || categoriesError || locationsError) {
        throw new Error('Failed to fetch data for backup');
      }

      // Create backup data object
      const backupData = {
        timestamp: new Date().toISOString(),
        tables: {
          assets: assets || [],
          categories: categories || [],
          locations: locations || []
        }
      };

      // Create and download backup file
      const dataStr = JSON.stringify(backupData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `asset-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "Backup Berhasil",
        description: "Database telah berhasil di-backup dan diunduh",
      });
    } catch (error) {
      console.error('Backup error:', error);
      toast({
        title: "Backup Gagal",
        description: "Terjadi kesalahan saat backup database",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDatabaseTransfer = async () => {
    setLoading(true);
    try {
      // Get current data
      const { data: assets, error: assetsError } = await supabase.from('assets').select('*');
      const { data: categories, error: categoriesError } = await supabase.from('categories').select('*');
      const { data: locations, error: locationsError } = await supabase.from('locations').select('*');

      if (assetsError || categoriesError || locationsError) {
        throw new Error('Failed to fetch data for transfer');
      }

      // Create transfer report
      const transferData = {
        timestamp: new Date().toISOString(),
        summary: {
          assets_count: assets?.length || 0,
          categories_count: categories?.length || 0,
          locations_count: locations?.length || 0
        },
        data: {
          assets: assets || [],
          categories: categories || [],
          locations: locations || []
        }
      };

      // Generate transfer report file
      const dataStr = JSON.stringify(transferData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `transfer-report-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "Transfer Berhasil",
        description: `Data berhasil diekspor: ${transferData.summary.assets_count} aset, ${transferData.summary.categories_count} kategori, ${transferData.summary.locations_count} lokasi`,
      });
    } catch (error) {
      console.error('Transfer error:', error);
      toast({
        title: "Transfer Gagal",
        description: "Terjadi kesalahan saat transfer database",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async (provider: string) => {
    setConnectionStatus('testing');
    try {
      if (provider === 'Supabase') {
        // Test current Supabase connection
        const { error } = await supabase.from('assets').select('count(*)', { count: 'exact', head: true });
        if (error) throw error;
        
        setConnectionStatus('connected');
        toast({
          title: "Koneksi Berhasil",
          description: `Berhasil terhubung ke ${provider} - Database aktif`,
        });
      } else {
        // For other providers, check if connection string is provided
        const connectionInputs = document.querySelectorAll('input[placeholder*="database URL"], input[placeholder*="Connection String"]');
        let hasConnectionString = false;
        
        connectionInputs.forEach(input => {
          if (input instanceof HTMLInputElement && input.value.trim()) {
            hasConnectionString = true;
          }
        });

        if (!hasConnectionString) {
          throw new Error('Connection string required');
        }

        // Simulate connection test for other providers
        await new Promise(resolve => setTimeout(resolve, 1500));
        setConnectionStatus('connected');
        toast({
          title: "Koneksi Berhasil",
          description: `Konfigurasi ${provider} tersimpan (simulasi - implementasi sebenarnya diperlukan)`,
        });
      }
    } catch (error) {
      setConnectionStatus('disconnected');
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({
        title: "Koneksi Gagal",
        description: `Gagal terhubung ke ${provider}: ${errorMessage}`,
        variant: "destructive",
      });
    }
  };

  const DatabaseCard = ({
    title,
    description,
    icon: Icon,
    provider,
    color
  }: {
    title: string;
    description: string;
    icon: any;
    provider: string;
    color: string;
  }) => {
    const isMysql = provider === 'MySQL';

    return (
      <Card className="h-full">
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${color}`}>
              <Icon className="w-5 h-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg">{title}</CardTitle>
              <CardDescription className="text-sm">{description}</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            {isMysql ? (
              <>
                <div className="space-y-2">
                  <Label>Host</Label>
                  <Input placeholder="localhost atau IP address" defaultValue="localhost" />
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <div className="space-y-2">
                    <Label>Port</Label>
                    <Input placeholder="3306" defaultValue="3306" />
                  </div>
                  <div className="space-y-2">
                    <Label>Database</Label>
                    <Input placeholder="simaset_db" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Username</Label>
                  <Input placeholder="root" defaultValue="root" />
                </div>
                <div className="space-y-2">
                  <Label>Password</Label>
                  <Input type="password" placeholder="Masukkan password MySQL" />
                </div>
                <div className="space-y-2">
                  <Label>Connection String (Optional)</Label>
                  <Input placeholder="mysql://username:password@host:port/database" />
                  <p className="text-xs text-muted-foreground">
                    Akan dibuat otomatis dari field di atas jika kosong
                  </p>
                </div>
              </>
            ) : (
              <>
                <div className="space-y-2">
                  <Label>Database URL</Label>
                  <Input placeholder={`Masukkan ${title} database URL`} />
                </div>
                <div className="space-y-2">
                  <Label>API Key / Password</Label>
                  <Input type="password" placeholder="Masukkan API key atau password" />
                </div>
                <div className="space-y-2">
                  <Label>Database Name</Label>
                  <Input placeholder="Nama database" />
                </div>
              </>
            )}
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${
                connectionStatus === 'connected' ? 'bg-green-500' :
                connectionStatus === 'testing' ? 'bg-yellow-500' : 'bg-red-500'
              }`} />
              <span className="text-sm text-muted-foreground">
                {connectionStatus === 'connected' ? 'Terhubung' :
                 connectionStatus === 'testing' ? 'Menguji...' : 'Terputus'}
              </span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => testConnection(provider)}
              disabled={connectionStatus === 'testing'}
            >
              {connectionStatus === 'testing' ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Testing...
                </>
              ) : (
                <>
                  <Shield className="w-4 h-4 mr-2" />
                  Test Koneksi
                </>
              )}
            </Button>
          </div>

          <div className="flex gap-2">
            <Button className="flex-1" size="sm">
              <CheckCircle className="w-4 h-4 mr-2" />
              Simpan
            </Button>
            <Button variant="outline" className="flex-1" size="sm">
              <AlertCircle className="w-4 h-4 mr-2" />
              Reset
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="p-2 bg-primary/10 rounded-lg">
          <SettingsIcon className="w-6 h-6 text-primary" />
        </div>
        <div>
          <h1 className="text-2xl font-bold">Pengaturan Sistem</h1>
          <p className="text-muted-foreground">Kelola konfigurasi dan database sistem</p>
        </div>
      </div>

      <Tabs defaultValue="database" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 lg:grid-cols-5">
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="mysql">MySQL</TabsTrigger>
          <TabsTrigger value="backup">Backup</TabsTrigger>
          <TabsTrigger value="transfer">Transfer</TabsTrigger>
          <TabsTrigger value="general">Umum</TabsTrigger>
        </TabsList>

        {/* Database Setup Tab */}
        <TabsContent value="database" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-5 h-5" />
                Konfigurasi Database
              </CardTitle>
              <CardDescription>
                Setup dan kelola koneksi database untuk sistem manajemen aset
              </CardDescription>
            </CardHeader>
          </Card>

          <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-4">
            <DatabaseCard
              title="Supabase"
              description="PostgreSQL database dengan real-time capabilities"
              icon={Database}
              provider="Supabase"
              color="bg-green-600"
            />

            <DatabaseCard
              title="MySQL"
              description="Relational database management system"
              icon={Database}
              provider="MySQL"
              color="bg-orange-600"
            />

            <DatabaseCard
              title="Aiven"
              description="Managed cloud database services"
              icon={Cloud}
              provider="Aiven"
              color="bg-blue-600"
            />

            <DatabaseCard
              title="NeonDB"
              description="Serverless PostgreSQL database"
              icon={Server}
              provider="NeonDB"
              color="bg-purple-600"
            />
          </div>
        </TabsContent>

        {/* MySQL Configuration Tab */}
        <TabsContent value="mysql" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Database className="w-5 h-5" />
                  Konfigurasi MySQL
                </div>
                {hasConfigChanged(mysqlConfig) && (
                  <Badge variant="outline" className="text-orange-600 border-orange-600">
                    Perubahan Belum Disimpan
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                Setup dan kelola koneksi MySQL untuk sistem manajemen aset. Konfigurasi disimpan di file .env
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Environment Info */}
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-start gap-3">
                  <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-900">Konfigurasi Environment Variables</h4>
                    <p className="text-sm text-blue-700 mt-1">
                      Konfigurasi MySQL disimpan dalam file .env untuk keamanan. Setelah mengubah pengaturan,
                      Anda perlu memperbarui file .env dan restart aplikasi untuk menerapkan perubahan.
                    </p>
                    <div className="mt-2 text-xs text-blue-600">
                      <strong>Status:</strong> {hasConfigChanged(mysqlConfig) ? 'Ada perubahan yang belum disimpan' : 'Sinkron dengan environment'}
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid gap-6 md:grid-cols-2">
                {/* Connection Settings */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Pengaturan Koneksi</CardTitle>
                    <CardDescription>Konfigurasi dasar koneksi MySQL</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Host</Label>
                        <Input
                          placeholder="localhost"
                          value={mysqlConfig.host}
                          onChange={(e) => handleMysqlConfigChange('host', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Port</Label>
                        <Input
                          placeholder="3306"
                          value={mysqlConfig.port}
                          onChange={(e) => handleMysqlConfigChange('port', e.target.value)}
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Database Name</Label>
                      <Input
                        placeholder="simaset_db"
                        value={mysqlConfig.database}
                        onChange={(e) => handleMysqlConfigChange('database', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Username</Label>
                      <Input
                        placeholder="root"
                        value={mysqlConfig.username}
                        onChange={(e) => handleMysqlConfigChange('username', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Password</Label>
                      <Input
                        type="password"
                        placeholder="Masukkan password MySQL"
                        value={mysqlConfig.password}
                        onChange={(e) => handleMysqlConfigChange('password', e.target.value)}
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Advanced Settings */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Pengaturan Lanjutan</CardTitle>
                    <CardDescription>Konfigurasi tambahan untuk optimasi</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Connection Pool Size</Label>
                      <Input
                        type="number"
                        placeholder="10"
                        value={mysqlConfig.poolSize}
                        onChange={(e) => handleMysqlConfigChange('poolSize', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Connection Timeout (detik)</Label>
                      <Input
                        type="number"
                        placeholder="30"
                        value={mysqlConfig.timeout}
                        onChange={(e) => handleMysqlConfigChange('timeout', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Charset</Label>
                      <select
                        className="w-full p-2 border rounded-md"
                        value={mysqlConfig.charset}
                        onChange={(e) => handleMysqlConfigChange('charset', e.target.value)}
                      >
                        <option value="utf8mb4">utf8mb4</option>
                        <option value="utf8">utf8</option>
                        <option value="latin1">latin1</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label>SSL Mode</Label>
                      <select
                        className="w-full p-2 border rounded-md"
                        value={mysqlConfig.sslMode}
                        onChange={(e) => handleMysqlConfigChange('sslMode', e.target.value)}
                      >
                        <option value="disabled">Disabled</option>
                        <option value="preferred">Preferred</option>
                        <option value="required">Required</option>
                      </select>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Connection String */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Connection String</CardTitle>
                  <CardDescription>String koneksi yang akan digunakan</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Generated Connection String</Label>
                    <Textarea
                      readOnly
                      value={generateMySQLConnectionString(mysqlConfig)}
                      className="font-mono text-sm"
                    />
                    <p className="text-xs text-muted-foreground">
                      String ini dibuat otomatis berdasarkan pengaturan di atas
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Environment Configuration Instructions */}
              {showEnvInstructions && (
                <Card className="border-orange-200 bg-orange-50">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <AlertCircle className="w-5 h-5 text-orange-600" />
                      Instruksi Konfigurasi Environment
                    </CardTitle>
                    <CardDescription>
                      Untuk menerapkan konfigurasi MySQL, ikuti langkah-langkah berikut:
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-orange-600 text-white text-sm flex items-center justify-center font-bold">1</div>
                        <div>
                          <p className="font-medium">Unduh file .env yang telah diperbarui</p>
                          <p className="text-sm text-muted-foreground">File .env baru telah diunduh dengan konfigurasi MySQL terbaru</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-orange-600 text-white text-sm flex items-center justify-center font-bold">2</div>
                        <div>
                          <p className="font-medium">Ganti file .env di root project</p>
                          <p className="text-sm text-muted-foreground">Backup file .env lama dan ganti dengan file yang baru diunduh</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 rounded-full bg-orange-600 text-white text-sm flex items-center justify-center font-bold">3</div>
                        <div>
                          <p className="font-medium">Restart aplikasi</p>
                          <p className="text-sm text-muted-foreground">Hentikan server development dan jalankan kembali untuk memuat konfigurasi baru</p>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div className="space-y-2">
                      <Label>Konfigurasi MySQL untuk .env:</Label>
                      <Textarea
                        readOnly
                        value={generateMySQLEnvContent(mysqlConfig)}
                        className="font-mono text-sm h-32"
                      />
                    </div>

                    <div className="flex gap-2">
                      <Button variant="outline" onClick={copyConfigToClipboard} className="flex-1">
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Salin Konfigurasi
                      </Button>
                      <Button variant="outline" onClick={() => downloadEnvFile(mysqlConfig)} className="flex-1">
                        <Download className="w-4 h-4 mr-2" />
                        Unduh .env
                      </Button>
                      <Button variant="ghost" onClick={() => setShowEnvInstructions(false)}>
                        <AlertCircle className="w-4 h-4 mr-2" />
                        Tutup
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Test Connection */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Test Koneksi</CardTitle>
                  <CardDescription>Uji koneksi ke database MySQL</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${
                        connectionStatus === 'connected' ? 'bg-green-500' :
                        connectionStatus === 'testing' ? 'bg-yellow-500 animate-pulse' : 'bg-red-500'
                      }`} />
                      <div>
                        <p className="font-medium">
                          {connectionStatus === 'connected' ? 'Koneksi Berhasil' :
                           connectionStatus === 'testing' ? 'Menguji Koneksi...' : 'Koneksi Terputus'}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {connectionStatus === 'connected' ? 'Database MySQL siap digunakan' :
                           connectionStatus === 'testing' ? 'Sedang menguji koneksi ke MySQL' : 'Periksa konfigurasi koneksi'}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => testConnection('MySQL')}
                      disabled={connectionStatus === 'testing'}
                    >
                      {connectionStatus === 'testing' ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          Testing...
                        </>
                      ) : (
                        <>
                          <Shield className="w-4 h-4 mr-2" />
                          Test Koneksi
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <div className="flex gap-4">
                <Button className="flex-1" onClick={saveMysqlConfig}>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Simpan Konfigurasi
                </Button>
                <Button variant="outline" className="flex-1" onClick={resetMysqlConfig}>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Reset ke Default
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Database Backup Tab */}
        <TabsContent value="backup" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="w-5 h-5" />
                Database Backup
              </CardTitle>
              <CardDescription>
                Backup dan restore data sistem manajemen aset
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Manual Backup</CardTitle>
                    <CardDescription>Buat backup manual database saat ini</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button 
                      onClick={handleDatabaseBackup}
                      disabled={loading}
                      className="w-full"
                    >
                      {loading ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          Membackup...
                        </>
                      ) : (
                        <>
                          <Download className="w-4 h-4 mr-2" />
                          Buat Backup
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Restore Backup</CardTitle>
                    <CardDescription>Restore database dari file backup</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Input 
                      type="file" 
                      accept=".json,.sql,.backup" 
                      onChange={async (e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          try {
                            const text = await file.text();
                            const backupData = JSON.parse(text);
                            
                            if (backupData.tables) {
                              toast({
                                title: "File Backup Valid",
                                description: `File backup berisi ${backupData.tables.assets?.length || 0} aset`,
                              });
                            }
                          } catch (error) {
                            toast({
                              title: "File Invalid",
                              description: "Format file backup tidak valid",
                              variant: "destructive",
                            });
                          }
                        }
                      }}
                    />
                    <Button 
                      variant="outline" 
                      className="w-full"
                      onClick={async () => {
                        const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
                        const file = fileInput?.files?.[0];
                        
                        if (!file) {
                          toast({
                            title: "File Diperlukan",
                            description: "Pilih file backup terlebih dahulu",
                            variant: "destructive",
                          });
                          return;
                        }

                        try {
                          const text = await file.text();
                          const backupData = JSON.parse(text);
                          
                          if (backupData.tables) {
                            toast({
                              title: "Restore Berhasil",
                              description: "File backup telah divalidasi dan siap untuk restore",
                            });
                          }
                        } catch (error) {
                          toast({
                            title: "Restore Gagal",
                            description: "Format file tidak valid",
                            variant: "destructive",
                          });
                        }
                      }}
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Restore Database
                    </Button>
                  </CardContent>
                </Card>
              </div>
              
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Automatic Backup</CardTitle>
                  <CardDescription>Konfigurasi backup otomatis</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="space-y-2">
                      <Label>Frekuensi</Label>
                      <select className="w-full p-2 border rounded-md">
                        <option>Harian</option>
                        <option>Mingguan</option>
                        <option>Bulanan</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label>Waktu</Label>
                      <Input type="time" defaultValue="02:00" />
                    </div>
                    <div className="space-y-2">
                      <Label>Retensi (hari)</Label>
                      <Input type="number" defaultValue="30" />
                    </div>
                  </div>
                  <Button>
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Simpan Konfigurasi
                  </Button>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Database Transfer Tab */}
        <TabsContent value="transfer" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RefreshCw className="w-5 h-5" />
                Database Transfer
              </CardTitle>
              <CardDescription>
                Transfer data antar database dan migrasi sistem
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Source Database</CardTitle>
                    <CardDescription>Database sumber untuk transfer</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Provider</Label>
                      <select className="w-full p-2 border rounded-md">
                        <option>Supabase</option>
                        <option>Aiven</option>
                        <option>NeonDB</option>
                        <option>PostgreSQL</option>
                        <option>MySQL</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label>Connection String</Label>
                      <Input placeholder="postgresql://username:password@host:port/database" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Target Database</CardTitle>
                    <CardDescription>Database tujuan untuk transfer</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Provider</Label>
                      <select className="w-full p-2 border rounded-md">
                        <option>Supabase</option>
                        <option>Aiven</option>
                        <option>NeonDB</option>
                        <option>PostgreSQL</option>
                        <option>MySQL</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label>Connection String</Label>
                      <Input placeholder="postgresql://username:password@host:port/database" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Transfer Options</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-3">
                      <Label className="text-base">Tables to Transfer</Label>
                      <div className="space-y-2">
                        <label className="flex items-center space-x-2">
                          <input type="checkbox" defaultChecked />
                          <span>Assets</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input type="checkbox" defaultChecked />
                          <span>Categories</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input type="checkbox" defaultChecked />
                          <span>Locations</span>
                        </label>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <Label className="text-base">Transfer Mode</Label>
                      <div className="space-y-2">
                        <label className="flex items-center space-x-2">
                          <input type="radio" name="mode" defaultChecked />
                          <span>Full Transfer</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input type="radio" name="mode" />
                          <span>Incremental</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input type="radio" name="mode" />
                          <span>Schema Only</span>
                        </label>
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <Button 
                    onClick={handleDatabaseTransfer}
                    disabled={loading}
                    className="w-full"
                  >
                    {loading ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Mentransfer...
                      </>
                    ) : (
                      <>
                        <Upload className="w-4 h-4 mr-2" />
                        Mulai Transfer
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>

        {/* General Settings Tab */}
        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Pengaturan Umum</CardTitle>
              <CardDescription>Konfigurasi umum sistem manajemen aset</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Nama Organisasi</Label>
                  <Input defaultValue="Universitas Widyagama" />
                </div>
                <div className="space-y-2">
                  <Label>Format Kode Aset</Label>
                  <Input defaultValue="WG-{000}" placeholder="WG-{000}" />
                </div>
                <div className="space-y-2">
                  <Label>Zona Waktu</Label>
                  <select className="w-full p-2 border rounded-md">
                    <option>Asia/Jakarta (WIB)</option>
                    <option>Asia/Makassar (WITA)</option>
                    <option>Asia/Jayapura (WIT)</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label>Bahasa Sistem</Label>
                  <select className="w-full p-2 border rounded-md">
                    <option>Bahasa Indonesia</option>
                    <option>English</option>
                  </select>
                </div>
              </div>
              
              <Separator />
              
              <Button>
                <CheckCircle className="w-4 h-4 mr-2" />
                Simpan Pengaturan
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Settings;