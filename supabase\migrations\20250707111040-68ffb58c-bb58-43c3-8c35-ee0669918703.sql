-- Create assets table
CREATE TABLE public.assets (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  asset_code TEXT NOT NULL UNIQUE,
  asset_name TEXT NOT NULL,
  category TEXT NOT NULL,
  location TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'Aktif',
  purchase_date DATE,
  value DECIMAL(15,2),
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create categories table
CREATE TABLE public.categories (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create locations table
CREATE TABLE public.locations (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  address TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Insert default categories
INSERT INTO public.categories (name, description) VALUES
('Komputer', 'Peralatan komputer dan laptop'),
('Furniture', 'Meja, kursi, dan perabotan kantor'),
('Elektronik', 'Peralatan elektronik kantor'),
('Kendaraan', 'Kendaraan operasional'),
('Peralatan', 'Peralatan dan tools kerja');

-- Insert default locations
INSERT INTO public.locations (name, description, address) VALUES
('Gedung A - Lt 1', 'Lantai 1 Gedung A', 'Jl. Raya Widyagama No. 1'),
('Gedung A - Lt 2', 'Lantai 2 Gedung A', 'Jl. Raya Widyagama No. 1'),
('Gedung B - Lt 1', 'Lantai 1 Gedung B', 'Jl. Raya Widyagama No. 2'),
('Lab Komputer', 'Laboratorium Komputer', 'Gedung C Lt. 2'),
('Ruang Server', 'Ruang Server IT', 'Gedung A Basement');

-- Insert sample assets
INSERT INTO public.assets (asset_code, asset_name, category, location, status, purchase_date, value, description) VALUES
('WG-001', 'Laptop Dell Inspiron 15', 'Komputer', 'Gedung A - Lt 1', 'Aktif', '2024-01-15', 8500000, 'Laptop untuk staff administrasi'),
('WG-002', 'Meja Kerja Kayu Jati', 'Furniture', 'Gedung A - Lt 1', 'Aktif', '2023-12-10', 2500000, 'Meja kerja untuk ruang direktur'),
('WG-003', 'Printer HP LaserJet', 'Elektronik', 'Gedung A - Lt 2', 'Aktif', '2024-02-20', 3200000, 'Printer untuk keperluan kantor'),
('WG-004', 'Kursi Ergonomis', 'Furniture', 'Gedung B - Lt 1', 'Aktif', '2024-01-05', 1800000, 'Kursi kerja ergonomis'),
('WG-005', 'Server Dell PowerEdge', 'Komputer', 'Ruang Server', 'Aktif', '2023-11-30', 45000000, 'Server utama sistem informasi');

-- Enable Row Level Security
ALTER TABLE public.assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;

-- Create policies (for now, allow all operations - adjust based on authentication needs)
CREATE POLICY "Allow all operations on assets" ON public.assets FOR ALL USING (true);
CREATE POLICY "Allow all operations on categories" ON public.categories FOR ALL USING (true);  
CREATE POLICY "Allow all operations on locations" ON public.locations FOR ALL USING (true);

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_assets_updated_at
  BEFORE UPDATE ON public.assets
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();