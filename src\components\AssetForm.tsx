import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Save, X } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { db, Asset } from '@/lib/database-adapter';

// Asset type is now imported from database-adapter

type AssetFormProps = {
  asset?: Asset;
  isEdit?: boolean;
  onSuccess?: () => void;
  trigger?: React.ReactNode;
};

const AssetForm = ({ asset, isEdit = false, onSuccess, trigger }: AssetFormProps) => {
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<string[]>([]);
  const [locations, setLocations] = useState<string[]>([]);
  const [formData, setFormData] = useState<Asset>({
    asset_code: '',
    asset_name: '',
    category: '',
    location: '',
    status: 'Aktif',
    purchase_date: '',
    value: 0,
    description: '',
  });

  useEffect(() => {
    if (asset && isEdit) {
      setFormData({
        ...asset,
        purchase_date: asset.purchase_date ? asset.purchase_date.split('T')[0] : '',
      });
    }
    fetchCategories();
    fetchLocations();
  }, [asset, isEdit]);

  const fetchCategories = async () => {
    try {
      const { data, error } = await db.categories.getAll();

      if (error) throw error;
      setCategories(data?.map(item => item.name) || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchLocations = async () => {
    try {
      const { data, error } = await db.locations.getAll();

      if (error) throw error;
      setLocations(data?.map(item => item.name) || []);
    } catch (error) {
      console.error('Error fetching locations:', error);
    }
  };

  const generateAssetCode = async () => {
    try {
      const { data, error } = await db.assets.getAll();

      if (error) throw error;

      if (data && data.length > 0) {
        // Sort by asset_code to get the latest
        const sortedAssets = data.sort((a, b) => b.asset_code.localeCompare(a.asset_code));
        const lastCode = sortedAssets[0].asset_code;
        const number = parseInt(lastCode.split('-')[1]) + 1;
        return `WG-${number.toString().padStart(3, '0')}`;
      } else {
        return 'WG-001';
      }
    } catch (error) {
      console.error('Error generating asset code:', error);
      return 'WG-001';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      let dataToSubmit = { ...formData };

      if (!isEdit && !dataToSubmit.asset_code) {
        dataToSubmit.asset_code = await generateAssetCode();
      }

      if (isEdit && asset?.id) {
        const { error } = await db.assets.update(asset.id, dataToSubmit);

        if (error) throw error;

        toast({
          title: "Berhasil",
          description: "Aset berhasil diperbarui",
        });
      } else {
        const { error } = await db.assets.create(dataToSubmit);

        if (error) throw error;

        toast({
          title: "Berhasil",
          description: "Aset berhasil ditambahkan",
        });
      }

      setOpen(false);
      setFormData({
        asset_code: '',
        asset_name: '',
        category: '',
        location: '',
        status: 'Aktif',
        purchase_date: '',
        value: 0,
        description: '',
      });
      
      if (onSuccess) onSuccess();
    } catch (error) {
      console.error('Error saving asset:', error);
      toast({
        title: "Error",
        description: `Gagal ${isEdit ? 'memperbarui' : 'menambahkan'} aset`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof Asset, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Tambah Aset
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEdit ? 'Edit Aset' : 'Tambah Aset Baru'}
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="asset_code">Kode Aset</Label>
              <Input
                id="asset_code"
                value={formData.asset_code}
                onChange={(e) => handleInputChange('asset_code', e.target.value)}
                placeholder="Otomatis jika kosong"
                disabled={isEdit}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="asset_name">Nama Aset *</Label>
              <Input
                id="asset_name"
                value={formData.asset_name}
                onChange={(e) => handleInputChange('asset_name', e.target.value)}
                placeholder="Masukkan nama aset"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">Kategori *</Label>
              <Select
                value={formData.category}
                onValueChange={(value) => handleInputChange('category', value)}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih kategori" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="location">Lokasi *</Label>
              <Select
                value={formData.location}
                onValueChange={(value) => handleInputChange('location', value)}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih lokasi" />
                </SelectTrigger>
                <SelectContent>
                  {locations.map((location) => (
                    <SelectItem key={location} value={location}>
                      {location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange('status', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Aktif">Aktif</SelectItem>
                  <SelectItem value="Tidak Aktif">Tidak Aktif</SelectItem>
                  <SelectItem value="Maintenance">Maintenance</SelectItem>
                  <SelectItem value="Rusak">Rusak</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="purchase_date">Tanggal Pembelian</Label>
              <Input
                id="purchase_date"
                type="date"
                value={formData.purchase_date}
                onChange={(e) => handleInputChange('purchase_date', e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="value">Nilai Aset (Rp)</Label>
            <Input
              id="value"
              type="number"
              value={formData.value}
              onChange={(e) => handleInputChange('value', parseInt(e.target.value) || 0)}
              placeholder="0"
              min="0"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Deskripsi</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Deskripsi tambahan tentang aset"
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
            >
              <X className="w-4 h-4 mr-2" />
              Batal
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                "Menyimpan..."
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  {isEdit ? 'Perbarui' : 'Simpan'}
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AssetForm;