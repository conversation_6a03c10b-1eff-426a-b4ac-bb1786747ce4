-- MySQL Migration Script for SIMASET UWG
-- Created: 2025-01-07
-- Description: Create all tables and initial data for asset management system

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS simaset_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE simaset_db;

-- Create assets table
CREATE TABLE assets (
  id VARCHAR(36) NOT NULL PRIMARY KEY DEFAULT (UUID()),
  asset_code VARCHAR(50) NOT NULL UNIQUE,
  asset_name VARCHAR(255) NOT NULL,
  category VARCHAR(100) NOT NULL,
  location VARCHAR(100) NOT NULL,
  status VARCHAR(50) NOT NULL DEFAULT 'Aktif',
  purchase_date DATE NULL,
  value DECIMAL(15,2) NULL,
  description TEXT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_asset_code (asset_code),
  INDEX idx_category (category),
  INDEX idx_location (location),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create categories table
CREATE TABLE categories (
  id VARCHAR(36) NOT NULL PRIMARY KEY DEFAULT (UUID()),
  name VARCHAR(100) NOT NULL UNIQUE,
  description TEXT NULL,
  icon VARCHAR(50) NULL DEFAULT 'Package',
  color VARCHAR(20) NULL DEFAULT 'text-blue-600',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create locations table
CREATE TABLE locations (
  id VARCHAR(36) NOT NULL PRIMARY KEY DEFAULT (UUID()),
  name VARCHAR(100) NOT NULL UNIQUE,
  description TEXT NULL,
  address TEXT NULL,
  building VARCHAR(50) NULL,
  floor VARCHAR(20) NULL,
  room VARCHAR(50) NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_name (name),
  INDEX idx_building (building)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create users table
CREATE TABLE users (
  id VARCHAR(36) NOT NULL PRIMARY KEY DEFAULT (UUID()),
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NULL,
  role ENUM('Administrator', 'Manager Aset', 'Staff', 'Viewer') NOT NULL DEFAULT 'Staff',
  status ENUM('Aktif', 'Nonaktif', 'Suspended') NOT NULL DEFAULT 'Aktif',
  department VARCHAR(100) NULL,
  phone VARCHAR(20) NULL,
  last_login TIMESTAMP NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_email (email),
  INDEX idx_role (role),
  INDEX idx_status (status),
  INDEX idx_department (department)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create documents table
CREATE TABLE documents (
  id VARCHAR(36) NOT NULL PRIMARY KEY DEFAULT (UUID()),
  name VARCHAR(255) NOT NULL,
  original_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_type VARCHAR(50) NOT NULL,
  file_size BIGINT NOT NULL,
  mime_type VARCHAR(100) NOT NULL,
  category VARCHAR(100) NOT NULL,
  asset_id VARCHAR(36) NULL,
  uploaded_by VARCHAR(36) NOT NULL,
  status ENUM('Pending', 'Review', 'Approved', 'Rejected') NOT NULL DEFAULT 'Pending',
  description TEXT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category (category),
  INDEX idx_asset_id (asset_id),
  INDEX idx_uploaded_by (uploaded_by),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE SET NULL,
  FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create asset_history table for tracking changes
CREATE TABLE asset_history (
  id VARCHAR(36) NOT NULL PRIMARY KEY DEFAULT (UUID()),
  asset_id VARCHAR(36) NOT NULL,
  action ENUM('CREATE', 'UPDATE', 'DELETE', 'TRANSFER', 'MAINTENANCE') NOT NULL,
  old_values JSON NULL,
  new_values JSON NULL,
  changed_by VARCHAR(36) NOT NULL,
  change_reason TEXT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_asset_id (asset_id),
  INDEX idx_action (action),
  INDEX idx_changed_by (changed_by),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
  FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create settings table for application configuration
CREATE TABLE settings (
  id VARCHAR(36) NOT NULL PRIMARY KEY DEFAULT (UUID()),
  setting_key VARCHAR(100) NOT NULL UNIQUE,
  setting_value TEXT NOT NULL,
  setting_type ENUM('string', 'number', 'boolean', 'json') NOT NULL DEFAULT 'string',
  description TEXT NULL,
  is_public BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_setting_key (setting_key),
  INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
