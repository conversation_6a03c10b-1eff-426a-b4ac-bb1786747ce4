import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Upload } from 'lucide-react';
import DocumentList from '@/components/documents/DocumentList';
import DocumentCategories from '@/components/documents/DocumentCategories';
import RecentDocuments from '@/components/documents/RecentDocuments';
import SharedDocuments from '@/components/documents/SharedDocuments';
import DocumentSearch from '@/components/documents/DocumentSearch';
import { documentsData, categoriesData } from '@/data/documentsData';

const Documents = () => {
  const [searchTerm, setSearchTerm] = useState('');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            Manajemen Dokumen
          </h1>
          <p className="text-gray-600 mt-2">Kelola dokumen dan berkas aset properti</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => console.log('Upload document')}>
            <Upload className="w-4 h-4 mr-2" />
            Upload Dokumen
          </Button>
          <Button onClick={() => console.log('Create folder')}>
            <Plus className="w-4 h-4 mr-2" />
            Buat Folder
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="documents" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="documents">Semua Dokumen</TabsTrigger>
          <TabsTrigger value="categories">Kategori</TabsTrigger>
          <TabsTrigger value="recent">Terbaru</TabsTrigger>
          <TabsTrigger value="shared">Dibagikan</TabsTrigger>
        </TabsList>

        <TabsContent value="documents" className="space-y-6">
          {/* Search and Filter */}
          <Card>
            <CardContent className="p-6">
              <DocumentSearch 
                searchTerm={searchTerm} 
                onSearchChange={setSearchTerm} 
              />
            </CardContent>
          </Card>

          {/* Documents List */}
          <Card>
            <CardHeader>
              <CardTitle>Daftar Dokumen</CardTitle>
            </CardHeader>
            <CardContent>
              <DocumentList documents={documentsData} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Kategori Dokumen</CardTitle>
              <Button onClick={() => console.log('Add document category')}>
                <Plus className="w-4 h-4 mr-2" />
                Tambah Kategori
              </Button>
            </CardHeader>
            <CardContent>
              <DocumentCategories categories={categoriesData} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recent" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Dokumen Terbaru</CardTitle>
            </CardHeader>
            <CardContent>
              <RecentDocuments documents={documentsData} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="shared" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Dokumen Dibagikan</CardTitle>
            </CardHeader>
            <CardContent>
              <SharedDocuments />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Documents;