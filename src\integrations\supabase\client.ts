// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://vodredfykoumhhglaxsn.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvZHJlZGZ5a291bWhoZ2xheHNuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4ODU4MjIsImV4cCI6MjA2NzQ2MTgyMn0.l4r9XMlK1pitGTMI37gVdXYUOTrviSU0JGxiVpABXIk";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});