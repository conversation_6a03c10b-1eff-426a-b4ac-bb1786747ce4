import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  File, 
  Image, 
  Eye, 
  Download, 
  Share, 
  Edit, 
  Trash2 
} from 'lucide-react';

interface Document {
  id: number;
  name: string;
  type: string;
  size: string;
  category: string;
  uploadDate: string;
  uploadedBy: string;
  status: string;
}

interface DocumentListProps {
  documents: Document[];
}

const DocumentList = ({ documents }: DocumentListProps) => {
  const getDocumentIcon = (type: string) => {
    switch (type) {
      case 'PDF':
        return <FileText className="w-6 h-6 text-blue-600" />;
      case 'Word':
        return <File className="w-6 h-6 text-blue-600" />;
      case 'Image':
        return <Image className="w-6 h-6 text-blue-600" />;
      default:
        return <FileText className="w-6 h-6 text-blue-600" />;
    }
  };

  return (
    <div className="space-y-4">
      {documents.map((doc) => (
        <div key={doc.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              {getDocumentIcon(doc.type)}
            </div>
            <div>
              <h3 className="font-semibold">{doc.name}</h3>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <span>{doc.size}</span>
                <span>{doc.category}</span>
                <span>Upload: {doc.uploadDate}</span>
                <span>Oleh: {doc.uploadedBy}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant={doc.status === 'Approved' ? 'default' : 'secondary'}>
              {doc.status}
            </Badge>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => console.log('View document:', doc.id)}>
                <Eye className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={() => console.log('Download document:', doc.id)}>
                <Download className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={() => console.log('Share document:', doc.id)}>
                <Share className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={() => console.log('Edit document:', doc.id)}>
                <Edit className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={() => console.log('Delete document:', doc.id)}>
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default DocumentList;