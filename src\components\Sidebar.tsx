
import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Building2, 
  BarChart3, 
  FileText, 
  Settings, 
  Users, 
  Home,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';

const Sidebar = () => {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();

  const menuItems = [
    { icon: Home, label: 'Dashboard', active: true, href: '/' },
    { icon: Building2, label: 'Manajemen Aset', active: false, href: '/assets' },
    { icon: BarChart3, label: 'Laporan', active: false, href: '/reports' },
    { icon: FileText, label: 'Dokumen', active: false, href: '/documents' },
    { icon: Users, label: 'Pengguna', active: false, href: '/users' },
    { icon: Settings, label: 'Pengaturan', active: false, href: '/settings' },
  ];

  const handleMenuClick = (label: string, href: string) => {
    console.log(`Navigating to: ${label}`);
  };

  return (
    <div 
      className={cn(
        "fixed left-0 top-0 h-full bg-white shadow-xl border-r border-gray-200 transition-all duration-300 z-50",
        collapsed ? "w-16" : "w-64"
      )}
    >
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          {!collapsed && (
            <div>
              <h2 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Widya Gama
              </h2>
              <p className="text-sm text-gray-500">Asset Management</p>
            </div>
          )}
          <button
            onClick={() => setCollapsed(!collapsed)}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
            aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {collapsed ? (
              <ChevronRight className="w-4 h-4 text-gray-600" />
            ) : (
              <ChevronLeft className="w-4 h-4 text-gray-600" />
            )}
          </button>
        </div>
      </div>

      {/* Navigation */}
      <nav className="p-4">
        <ul className="space-y-2">
          {menuItems.map((item, index) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.href;
            return (
              <li key={index}>
                <Link
                  to={item.href}
                  onClick={() => handleMenuClick(item.label, item.href)}
                  className={cn(
                    "flex items-center p-3 rounded-lg transition-all duration-200 group w-full text-left",
                    isActive
                      ? "bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-md"
                      : "text-gray-700 hover:bg-gray-100 hover:text-blue-600"
                  )}
                >
                  <Icon className={cn(
                    "w-5 h-5 transition-colors",
                    collapsed ? "mx-auto" : "mr-3"
                  )} />
                  {!collapsed && (
                    <span className="font-medium">{item.label}</span>
                  )}
                  {isActive && !collapsed && (
                    <div className="ml-auto w-2 h-2 bg-white rounded-full"></div>
                  )}
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* User Profile */}
      {!collapsed && (
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
          <div className="flex items-center p-3 rounded-lg bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
              A
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">Admin</p>
              <p className="text-xs text-gray-500"><EMAIL></p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
