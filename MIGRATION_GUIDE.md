# Panduan Migrasi Database SIMASET UWG

## Overview
Panduan ini menjelaskan cara melakukan migrasi dari Supabase ke MySQL untuk sistem manajemen aset SIMASET UWG.

## Mengapa Migrasi ke MySQL?

### Keuntungan MySQL:
- **Kontrol Penuh**: Database lokal memberikan kontrol penuh atas data
- **Performa**: Akses langsung ke database tanpa latency network
- **Biaya**: Tidak ada biaya subscription bulanan
- **Keamanan**: Data tersimpan di infrastruktur sendiri
- **Customization**: Dapat dikustomisasi sesuai kebutuhan spesifik

### Fitur yang Didukung:
- ✅ Semua operasi CRUD (Create, Read, Update, Delete)
- ✅ Pencarian dan filtering data
- ✅ Backup dan restore otomatis
- ✅ Audit trail dan history perubahan
- ✅ User management dan permissions
- ✅ Document management
- ✅ Reporting dan analytics

## Persiapan Migrasi

### 1. Install MySQL
```bash
# Windows
# Download dari https://dev.mysql.com/downloads/mysql/

# macOS
brew install mysql

# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# CentOS/RHEL
sudo yum install mysql-server
```

### 2. Konfigurasi MySQL
```sql
-- Login ke MySQL sebagai root
mysql -u root -p

-- Buat user untuk aplikasi
CREATE USER 'simaset'@'localhost' IDENTIFIED BY 'password_yang_kuat';

-- Berikan privileges
GRANT ALL PRIVILEGES ON simaset_db.* TO 'simaset'@'localhost';
FLUSH PRIVILEGES;
```

### 3. Update Environment Variables
Edit file `.env`:
```env
# MySQL Database Configuration
VITE_MYSQL_HOST=localhost
VITE_MYSQL_PORT=3306
VITE_MYSQL_DATABASE=simaset_db
VITE_MYSQL_USERNAME=simaset
VITE_MYSQL_PASSWORD=password_yang_kuat

# MySQL Advanced Configuration
VITE_MYSQL_POOL_SIZE=10
VITE_MYSQL_TIMEOUT=30
VITE_MYSQL_CHARSET=utf8mb4
VITE_MYSQL_SSL_MODE=disabled
```

## Proses Migrasi

### Metode 1: Menggunakan Script Otomatis

1. **Jalankan Setup Script**
   ```bash
   node scripts/setup-mysql.js
   ```

2. **Verifikasi Database**
   ```bash
   mysql -u simaset -p simaset_db
   SHOW TABLES;
   ```

3. **Migrasi Data via UI**
   - Buka aplikasi: `http://localhost:8080`
   - Masuk ke Settings > Tab Migrasi
   - Klik "Test Koneksi MySQL"
   - Klik "Mulai Migrasi"

### Metode 2: Manual Setup

1. **Buat Database**
   ```sql
   CREATE DATABASE simaset_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   USE simaset_db;
   ```

2. **Jalankan Migrasi**
   ```bash
   mysql -u simaset -p simaset_db < mysql-migrations/001_create_tables.sql
   mysql -u simaset -p simaset_db < mysql-migrations/002_insert_initial_data.sql
   ```

3. **Migrasi Data Supabase**
   - Gunakan UI Settings > Migrasi
   - Atau gunakan API endpoint `/api/migration/from-supabase`

## Struktur Database MySQL

### Tabel Utama:
- **assets**: Data aset organisasi
- **categories**: Kategori aset
- **locations**: Lokasi aset
- **users**: Data pengguna sistem
- **documents**: Dokumen terkait aset
- **asset_history**: Riwayat perubahan aset
- **settings**: Konfigurasi aplikasi

### Fitur Tambahan:
- **UUID Primary Keys**: Kompatibilitas dengan Supabase
- **Timestamps**: Auto-update created_at dan updated_at
- **Indexes**: Optimasi performa query
- **Foreign Keys**: Integritas referensial
- **JSON Support**: Untuk data kompleks

## Verifikasi Migrasi

### 1. Cek Koneksi Database
```bash
# Test koneksi
mysql -u simaset -p -e "SELECT 1"

# Cek database
mysql -u simaset -p -e "SHOW DATABASES"
```

### 2. Verifikasi Data
```sql
-- Cek jumlah data
SELECT 
  (SELECT COUNT(*) FROM assets) as assets_count,
  (SELECT COUNT(*) FROM categories) as categories_count,
  (SELECT COUNT(*) FROM locations) as locations_count;

-- Cek sample data
SELECT * FROM assets LIMIT 5;
SELECT * FROM categories;
SELECT * FROM locations;
```

### 3. Test Aplikasi
- Login ke aplikasi
- Cek halaman Assets
- Test CRUD operations
- Verifikasi search dan filter
- Test backup/restore

## Troubleshooting

### Error: "Access denied for user"
```sql
-- Reset password user
ALTER USER 'simaset'@'localhost' IDENTIFIED BY 'new_password';
FLUSH PRIVILEGES;
```

### Error: "Database connection failed"
1. Cek service MySQL berjalan
2. Verifikasi host dan port
3. Test koneksi manual
4. Cek firewall settings

### Error: "Table doesn't exist"
```bash
# Jalankan ulang migrasi
mysql -u simaset -p simaset_db < mysql-migrations/001_create_tables.sql
```

### Error: "Data migration failed"
1. Cek koneksi Supabase
2. Verifikasi API keys
3. Cek log error di browser console
4. Test migrasi per tabel

## Rollback Plan

### Jika Migrasi Gagal:
1. **Kembali ke Supabase**
   ```env
   # Kosongkan konfigurasi MySQL
   VITE_MYSQL_DATABASE=
   ```

2. **Backup Data MySQL**
   ```bash
   mysqldump -u simaset -p simaset_db > backup_mysql.sql
   ```

3. **Restore Supabase**
   - Data Supabase tetap utuh
   - Aplikasi otomatis kembali ke Supabase

## Maintenance

### Backup Rutin
```bash
# Daily backup
mysqldump -u simaset -p simaset_db > backup_$(date +%Y%m%d).sql

# Compressed backup
mysqldump -u simaset -p simaset_db | gzip > backup_$(date +%Y%m%d).sql.gz
```

### Monitoring
```sql
-- Cek ukuran database
SELECT 
  table_schema as 'Database',
  ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'simaset_db';

-- Cek performa query
SHOW PROCESSLIST;
```

### Update Schema
```sql
-- Backup sebelum update
mysqldump -u simaset -p simaset_db > backup_before_update.sql

-- Jalankan migration baru
mysql -u simaset -p simaset_db < mysql-migrations/003_new_migration.sql
```

## Support

### Log Files
- Application logs: Browser console
- MySQL logs: `/var/log/mysql/error.log`
- Migration logs: Settings > Migrasi tab

### Kontak Support
- Email: <EMAIL>
- Documentation: `/ENVIRONMENT_SETUP.md`
- Issues: GitHub repository

## Checklist Migrasi

- [ ] MySQL terinstall dan berjalan
- [ ] User dan database MySQL dibuat
- [ ] File .env dikonfigurasi
- [ ] Script setup-mysql.js berhasil
- [ ] Koneksi MySQL berhasil di UI
- [ ] Data Supabase berhasil dimigrasi
- [ ] Verifikasi data di MySQL
- [ ] Test semua fitur aplikasi
- [ ] Backup database MySQL
- [ ] Update dokumentasi tim
