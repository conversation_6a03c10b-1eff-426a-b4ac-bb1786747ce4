// Migration Service for SIMASET UWG
// This service handles data migration from Supabase to MySQL

import { supabase } from '@/integrations/supabase/client';
import { mysqlClient } from './mysql-client';
import { getMySQLConfig, validateMySQLConfig } from './env-config';

export interface MigrationProgress {
  step: string;
  progress: number;
  total: number;
  message: string;
  completed: boolean;
  error?: string;
}

export interface MigrationResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export class MigrationService {
  private onProgress?: (progress: MigrationProgress) => void;

  constructor(onProgress?: (progress: MigrationProgress) => void) {
    this.onProgress = onProgress;
  }

  private updateProgress(step: string, progress: number, total: number, message: string, completed = false, error?: string) {
    if (this.onProgress) {
      this.onProgress({
        step,
        progress,
        total,
        message,
        completed,
        error
      });
    }
  }

  // Validate MySQL configuration before migration
  async validateConfiguration(): Promise<MigrationResult> {
    try {
      this.updateProgress('validation', 0, 1, 'Memvalidasi konfigurasi MySQL...');

      const mysqlConfig = getMySQLConfig();
      const validation = validateMySQLConfig(mysqlConfig);

      if (!validation.isValid) {
        return {
          success: false,
          message: 'Konfigurasi MySQL tidak valid',
          error: validation.errors.join(', ')
        };
      }

      // Test MySQL connection
      try {
        await mysqlClient.database.testConnection();
        this.updateProgress('validation', 1, 1, 'Konfigurasi MySQL valid', true);
        return {
          success: true,
          message: 'Konfigurasi MySQL valid dan koneksi berhasil'
        };
      } catch (error) {
        return {
          success: false,
          message: 'Gagal terhubung ke MySQL',
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    } catch (error) {
      return {
        success: false,
        message: 'Error validasi konfigurasi',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Get data from Supabase
  async getSupabaseData(): Promise<MigrationResult> {
    try {
      this.updateProgress('fetch', 0, 3, 'Mengambil data dari Supabase...');

      // Fetch assets
      const { data: assets, error: assetsError } = await supabase
        .from('assets')
        .select('*')
        .order('created_at');

      if (assetsError) throw assetsError;
      this.updateProgress('fetch', 1, 3, `Berhasil mengambil ${assets?.length || 0} aset`);

      // Fetch categories
      const { data: categories, error: categoriesError } = await supabase
        .from('categories')
        .select('*')
        .order('name');

      if (categoriesError) throw categoriesError;
      this.updateProgress('fetch', 2, 3, `Berhasil mengambil ${categories?.length || 0} kategori`);

      // Fetch locations
      const { data: locations, error: locationsError } = await supabase
        .from('locations')
        .select('*')
        .order('name');

      if (locationsError) throw locationsError;
      this.updateProgress('fetch', 3, 3, `Berhasil mengambil ${locations?.length || 0} lokasi`, true);

      return {
        success: true,
        message: 'Data berhasil diambil dari Supabase',
        data: {
          assets: assets || [],
          categories: categories || [],
          locations: locations || []
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Gagal mengambil data dari Supabase',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Migrate data to MySQL
  async migrateToMySQL(data: any): Promise<MigrationResult> {
    try {
      const { assets, categories, locations } = data;
      const totalItems = (assets?.length || 0) + (categories?.length || 0) + (locations?.length || 0);
      let processedItems = 0;

      this.updateProgress('migrate', 0, totalItems, 'Memulai migrasi ke MySQL...');

      // Migrate categories first (referenced by assets)
      if (categories && categories.length > 0) {
        for (const category of categories) {
          try {
            await mysqlClient.categories.create({
              name: category.name,
              description: category.description
            });
            processedItems++;
            this.updateProgress('migrate', processedItems, totalItems, `Migrasi kategori: ${category.name}`);
          } catch (error) {
            console.warn(`Failed to migrate category ${category.name}:`, error);
          }
        }
      }

      // Migrate locations (referenced by assets)
      if (locations && locations.length > 0) {
        for (const location of locations) {
          try {
            await mysqlClient.locations.create({
              name: location.name,
              description: location.description,
              address: location.address
            });
            processedItems++;
            this.updateProgress('migrate', processedItems, totalItems, `Migrasi lokasi: ${location.name}`);
          } catch (error) {
            console.warn(`Failed to migrate location ${location.name}:`, error);
          }
        }
      }

      // Migrate assets
      if (assets && assets.length > 0) {
        for (const asset of assets) {
          try {
            await mysqlClient.assets.create({
              asset_code: asset.asset_code,
              asset_name: asset.asset_name,
              category: asset.category,
              location: asset.location,
              status: asset.status,
              purchase_date: asset.purchase_date,
              value: asset.value,
              description: asset.description
            });
            processedItems++;
            this.updateProgress('migrate', processedItems, totalItems, `Migrasi aset: ${asset.asset_name}`);
          } catch (error) {
            console.warn(`Failed to migrate asset ${asset.asset_code}:`, error);
          }
        }
      }

      this.updateProgress('migrate', totalItems, totalItems, 'Migrasi selesai', true);

      return {
        success: true,
        message: `Berhasil migrasi ${processedItems} item ke MySQL`,
        data: { processedItems, totalItems }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Gagal migrasi ke MySQL',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Full migration process
  async performFullMigration(): Promise<MigrationResult> {
    try {
      // Step 1: Validate configuration
      const validation = await this.validateConfiguration();
      if (!validation.success) {
        return validation;
      }

      // Step 2: Get data from Supabase
      const dataResult = await this.getSupabaseData();
      if (!dataResult.success) {
        return dataResult;
      }

      // Step 3: Migrate to MySQL
      const migrationResult = await this.migrateToMySQL(dataResult.data);
      if (!migrationResult.success) {
        return migrationResult;
      }

      return {
        success: true,
        message: 'Migrasi lengkap berhasil diselesaikan',
        data: migrationResult.data
      };
    } catch (error) {
      return {
        success: false,
        message: 'Gagal melakukan migrasi lengkap',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Verify migration results
  async verifyMigration(): Promise<MigrationResult> {
    try {
      this.updateProgress('verify', 0, 3, 'Memverifikasi hasil migrasi...');

      // Get counts from both databases
      const [supabaseStats, mysqlStats] = await Promise.all([
        this.getSupabaseStats(),
        mysqlClient.database.getStats()
      ]);

      this.updateProgress('verify', 3, 3, 'Verifikasi selesai', true);

      const comparison = {
        supabase: supabaseStats,
        mysql: mysqlStats,
        matches: {
          assets: supabaseStats.assets === mysqlStats.assets_count,
          categories: supabaseStats.categories === mysqlStats.categories_count,
          locations: supabaseStats.locations === mysqlStats.locations_count
        }
      };

      const allMatch = Object.values(comparison.matches).every(match => match);

      return {
        success: allMatch,
        message: allMatch ? 'Verifikasi berhasil - data cocok' : 'Verifikasi gagal - data tidak cocok',
        data: comparison
      };
    } catch (error) {
      return {
        success: false,
        message: 'Gagal memverifikasi migrasi',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async getSupabaseStats() {
    const [assetsResult, categoriesResult, locationsResult] = await Promise.all([
      supabase.from('assets').select('*', { count: 'exact', head: true }),
      supabase.from('categories').select('*', { count: 'exact', head: true }),
      supabase.from('locations').select('*', { count: 'exact', head: true }),
    ]);

    return {
      assets: assetsResult.count || 0,
      categories: categoriesResult.count || 0,
      locations: locationsResult.count || 0
    };
  }
}

// Export convenience functions
export const createMigrationService = (onProgress?: (progress: MigrationProgress) => void) => {
  return new MigrationService(onProgress);
};

export const quickMigration = async (): Promise<MigrationResult> => {
  const service = new MigrationService();
  return service.performFullMigration();
};
