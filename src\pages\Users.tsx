import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Users as UsersIcon, 
  Plus, 
  Search, 
  Filter,
  Mail,
  Phone,
  Shield,
  Settings,
  UserCheck,
  UserX
} from 'lucide-react';

const Users = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const users = [
    {
      id: 1,
      name: 'Dr. <PERSON>',
      email: '<EMAIL>',
      role: 'Administrator',
      status: 'Aktif',
      department: 'IT',
      lastLogin: '2 jam yang lalu'
    },
    {
      id: 2,
      name: '<PERSON><PERSON>, <PERSON><PERSON>',
      email: '<EMAIL>',
      role: 'Manager Aset',
      status: 'Aktif',
      department: '<PERSON><PERSON>',
      lastLogin: '1 hari yang lalu'
    },
    {
      id: 3,
      name: 'Budi <PERSON>o',
      email: '<EMAIL>',
      role: 'Staff',
      status: 'Nonaktif',
      department: 'Keuangan',
      lastLogin: '1 minggu yang lalu'
    }
  ];

  const roles = [
    { name: 'Administrator', count: 3, color: 'bg-red-100 text-red-800' },
    { name: 'Manager Aset', count: 5, color: 'bg-blue-100 text-blue-800' },
    { name: 'Staff', count: 12, color: 'bg-green-100 text-green-800' },
    { name: 'Viewer', count: 8, color: 'bg-gray-100 text-gray-800' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            Manajemen Pengguna
          </h1>
          <p className="text-gray-600 mt-2">Kelola akses dan hak pengguna sistem</p>
        </div>
        <Button onClick={() => console.log('Add user')}>
          <Plus className="w-4 h-4 mr-2" />
          Tambah Pengguna
        </Button>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="users" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="users">Daftar Pengguna</TabsTrigger>
          <TabsTrigger value="roles">Peran & Hak Akses</TabsTrigger>
          <TabsTrigger value="departments">Departemen</TabsTrigger>
          <TabsTrigger value="audit">Log Aktivitas</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-6">
          {/* Search and Filter */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div className="flex-1 w-full sm:w-auto">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Cari pengguna berdasarkan nama atau email..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={() => console.log('Filter users')}>
                    <Filter className="w-4 h-4 mr-2" />
                    Filter
                  </Button>
                  <Button variant="outline" onClick={() => console.log('Send invitation')}>
                    <Mail className="w-4 h-4 mr-2" />
                    Kirim Undangan
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Users List */}
          <Card>
            <CardHeader>
              <CardTitle>Daftar Pengguna Aktif</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {users.map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <Avatar>
                        <AvatarImage src="" />
                        <AvatarFallback>{user.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold">{user.name}</h3>
                        <p className="text-sm text-gray-600">{user.email}</p>
                        <p className="text-xs text-gray-500">{user.department}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <Badge variant={user.status === 'Aktif' ? 'default' : 'secondary'}>
                          {user.status}
                        </Badge>
                        <p className="text-xs text-gray-500 mt-1">{user.role}</p>
                        <p className="text-xs text-gray-500">{user.lastLogin}</p>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" onClick={() => console.log('Edit user:', user.id)}>
                          <Settings className="w-4 h-4" />
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => console.log('Toggle user status:', user.id)}>
                          {user.status === 'Aktif' ? <UserX className="w-4 h-4" /> : <UserCheck className="w-4 h-4" />}
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Peran & Hak Akses</CardTitle>
              <Button onClick={() => console.log('Add role')}>
                <Plus className="w-4 h-4 mr-2" />
                Tambah Peran
              </Button>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {roles.map((role, index) => (
                  <Card key={index} className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={() => console.log('View role:', role.name)}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Shield className="w-8 h-8 text-blue-600" />
                        <div>
                          <h3 className="font-semibold">{role.name}</h3>
                          <p className="text-sm text-gray-600">{role.count} pengguna</p>
                        </div>
                      </div>
                      <Badge className={role.color}>{role.count}</Badge>
                    </div>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="departments" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Departemen</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={() => console.log('View IT department')}>
                  <div className="flex items-center gap-3">
                    <UsersIcon className="w-8 h-8 text-blue-600" />
                    <div>
                      <h3 className="font-semibold">IT</h3>
                      <p className="text-sm text-gray-600">8 pengguna</p>
                    </div>
                  </div>
                </Card>
                <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={() => console.log('View Sarana Prasarana department')}>
                  <div className="flex items-center gap-3">
                    <UsersIcon className="w-8 h-8 text-green-600" />
                    <div>
                      <h3 className="font-semibold">Sarana Prasarana</h3>
                      <p className="text-sm text-gray-600">12 pengguna</p>
                    </div>
                  </div>
                </Card>
                <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={() => console.log('View Keuangan department')}>
                  <div className="flex items-center gap-3">
                    <UsersIcon className="w-8 h-8 text-purple-600" />
                    <div>
                      <h3 className="font-semibold">Keuangan</h3>
                      <p className="text-sm text-gray-600">8 pengguna</p>
                    </div>
                  </div>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audit" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Log Aktivitas Pengguna</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 border-l-4 border-blue-500 bg-blue-50">
                  <div>
                    <p className="font-medium">Dr. Ahmad Susanto login ke sistem</p>
                    <p className="text-sm text-gray-600">2 jam yang lalu</p>
                  </div>
                  <Badge>Login</Badge>
                </div>
                <div className="flex items-center justify-between p-3 border-l-4 border-green-500 bg-green-50">
                  <div>
                    <p className="font-medium">Siti Rahayu menambahkan aset baru</p>
                    <p className="text-sm text-gray-600">5 jam yang lalu</p>
                  </div>
                  <Badge>Create</Badge>
                </div>
                <div className="flex items-center justify-between p-3 border-l-4 border-yellow-500 bg-yellow-50">
                  <div>
                    <p className="font-medium">Budi Santoso mengubah data aset</p>
                    <p className="text-sm text-gray-600">1 hari yang lalu</p>
                  </div>
                  <Badge>Update</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Users;