import React, { useState, useEffect } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Edit, 
  Trash2, 
  Eye, 
  ArrowUpDown,
  Building2
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { db, Asset } from '@/lib/database-adapter';
import AssetForm from './AssetForm';

// Asset type is now imported from database-adapter

const AssetTable = () => {
  const { toast } = useToast();
  const [assets, setAssets] = useState<Asset[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAssets();
  }, []);

  const fetchAssets = async () => {
    try {
      setLoading(true);
      const { data, error } = await db.assets.getAll();

      if (error) throw error;
      setAssets(data || []);
    } catch (error) {
      console.error('Error fetching assets:', error);
      toast({
        title: "Error",
        description: "Gagal memuat data aset",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      const { error } = await db.assets.delete(id);

      if (error) throw error;

      toast({
        title: "Berhasil",
        description: "Aset berhasil dihapus",
      });

      fetchAssets(); // Refresh data
    } catch (error) {
      console.error('Error deleting asset:', error);
      toast({
        title: "Error",
        description: "Gagal menghapus aset",
        variant: "destructive",
      });
    }
  };

  const handleView = (id: string) => {
    toast({
      title: "Lihat Detail",
      description: `Melihat detail aset dengan ID: ${id}`,
    });
  };

  const formatRupiah = (value: number | null) => {
    if (!value) return '-';
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="text-muted-foreground">Memuat data aset...</div>
      </div>
    );
  }

  return (
    <div className="rounded-lg border bg-white">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50">
              <TableHead className="font-semibold">Kode Aset</TableHead>
              <TableHead className="font-semibold">Nama Aset</TableHead>
              <TableHead className="font-semibold">Kategori</TableHead>
              <TableHead className="font-semibold">Lokasi</TableHead>
              <TableHead className="font-semibold">Status</TableHead>
              <TableHead className="font-semibold">Tanggal Pembelian</TableHead>
              <TableHead className="font-semibold">Nilai</TableHead>
              <TableHead className="text-center font-semibold">Aksi</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {assets.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                  Tidak ada data aset
                </TableCell>
              </TableRow>
            ) : (
              assets.map((asset) => (
                <TableRow key={asset.id} className="hover:bg-muted/50">
                  <TableCell className="font-medium">{asset.asset_code}</TableCell>
                  <TableCell>{asset.asset_name}</TableCell>
                  <TableCell>
                    <Badge variant="secondary" className="flex items-center gap-1 w-fit">
                      <Building2 className="w-3 h-3" />
                      {asset.category}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-muted-foreground">{asset.location}</TableCell>
                  <TableCell>
                    <Badge 
                      variant={asset.status === 'Aktif' ? 'default' : 'secondary'}
                      className={asset.status === 'Aktif' ? 'bg-green-100 text-green-800 hover:bg-green-200' : ''}
                    >
                      {asset.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-muted-foreground">{formatDate(asset.purchase_date)}</TableCell>
                  <TableCell className="font-semibold">{formatRupiah(asset.value)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleView(asset.id)}
                        className="h-8 w-8 p-0"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <AssetForm
                        asset={asset}
                        isEdit={true}
                        onSuccess={fetchAssets}
                        trigger={
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        }
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(asset.id)}
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        
        {assets.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-2">
              <Building2 className="w-12 h-12 mx-auto" />
            </div>
            <p className="text-gray-500">Tidak ada data yang ditemukan</p>
            <p className="text-sm text-gray-400">Belum ada aset yang terdaftar dalam sistem</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AssetTable;